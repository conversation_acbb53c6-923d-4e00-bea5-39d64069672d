curl 'https://ccdiiyc-gx65500.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A44455436-ETMsDgAAAZhkPg%2B%2BABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEHo9x2flV2mHUAQiXOhxKuQAAABQoQg3LGyJVig3FKglXNk9cSbIReU1feq9GhCF83PmTgeNtbiGsebcjQxvRtC1SQuy0%2B0k3jb5DplmibNRBI46gOO7Fjur69Z1RzR0D3uoHukAFPq616%2BfHyfyDLf4wnTCJMTacKbb' \
  -H 'Referer: chrome-extension://odfafepnkmbhccpbejgmiehpchacaeak/document-blocked.html?details=%7B%22url%22%3A%22https%3A%2F%2F9rjj6kfe.r.ap-southeast-2.awstrack.me%2FL0%2Fhttps%3A%252F%252Fccdiiyc-gx65500.snowflakecomputing.com%252Fconsole%252Flogin%253FactivationToken%3Dver%25253A1-hint%25253A44455436-ETMsDgAAAZhkPg%25252B%25252BABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEHo9x2flV2mHUAQiXOhxKuQAAABQoQg3LGyJVig3FKglXNk9cSbIReU1feq9GhCF83PmTgeNtbiGsebcjQxvRtC1SQuy0%25252B0k3jb5DplmibNRBI46gOO7Fjur69Z1RzR0D3uoHukAFPq616%25252BfHyfyDLf4wnTCJMTacKbb%2F1%2F01080198643e2aeb-d525bbf5-204c-4915-9a25-97435d3fb9ac-000000%2FMSeUct9zpWy0DZ7JsskUngMrcDQ%3D218%22%2C%22dn%22%3A%22awstrack.me%22%2C%22fs%22%3A%22%7C%7Cawstrack.me%5E%22%2C%22hn%22%3A%229rjj6kfe.r.ap-southeast-2.awstrack.me%22%2C%22to%22%3A%22https%3A%2F%2Fccdiiyc-gx65500.snowflakecomputing.com%2Fconsole%2Flogin%3FactivationToken%3Dver%253A1-hint%253A44455436-ETMsDgAAAZhkPg%252B%252BABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEHo9x2flV2mHUAQiXOhxKuQAAABQoQg3LGyJVig3FKglXNk9cSbIReU1feq9GhCF83PmTgeNtbiGsebcjQxvRtC1SQuy0%252B0k3jb5DplmibNRBI46gOO7Fjur69Z1RzR0D3uoHukAFPq616%252BfHyfyDLf4wnTCJMTacKbb%22%7D' \
  -H 'Upgrade-Insecure-Requests: 1' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'

curl 'https://9rjj6kfe.r.ap-southeast-2.awstrack.me/L0/https:%2F%2Fccdiiyc-gx65500.snowflakecomputing.com%2Fconsole%2Flogin%3FactivationToken=ver%253A1-hint%253A44455436-ETMsDgAAAZhkPg%252B%252BABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEHo9x2flV2mHUAQiXOhxKuQAAABQoQg3LGyJVig3FKglXNk9cSbIReU1feq9GhCF83PmTgeNtbiGsebcjQxvRtC1SQuy0%252B0k3jb5DplmibNRBI46gOO7Fjur69Z1RzR0D3uoHukAFPq616%252BfHyfyDLf4wnTCJMTacKbb/1/01080198643e2aeb-d525bbf5-204c-4915-9a25-97435d3fb9ac-000000/MSeUct9zpWy0DZ7JsskUngMrcDQ=218' \
  -H 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'Connection: keep-alive' \
  -H 'Sec-Fetch-Dest: document' \
  -H 'Sec-Fetch-Mode: navigate' \
  -H 'Sec-Fetch-Site: cross-site' \
  -H 'Sec-Fetch-User: ?1' \
  -H 'Upgrade-Insecure-Requests: 1' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'


curl 'https://ccdiiyc-gx65500.snowflakecomputing.com/session/v1/login-request?__uiAppName=Login' \
  -X 'POST' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'Connection: keep-alive' \
  -H 'Content-Length: 611' \
  -b '_dd_s=rum=2&id=98d071a8-eb9c-46e5-8062-6380022f83ac&created=1754028583034&expire=1754029603488' \
  -H 'Origin: https://ccdiiyc-gx65500.snowflakecomputing.com' \
  -H 'Referer: https://ccdiiyc-gx65500.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A44455436-ETMsDgAAAZhkPg%2B%2BABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEHo9x2flV2mHUAQiXOhxKuQAAABQoQg3LGyJVig3FKglXNk9cSbIReU1feq9GhCF83PmTgeNtbiGsebcjQxvRtC1SQuy0%2B0k3jb5DplmibNRBI46gOO7Fjur69Z1RzR0D3uoHukAFPq616%2BfHyfyDLf4wnTCJMTacKbb' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' \
  -H 'content-type: application/json' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"'

{
  "data" : {
  "authnSubject" : {
    "loginUser" : {
      "loginName" : "HEWSEEP",
      "firstName" : "rwe",
      "lastName" : "rwqe",
      "email" : "<EMAIL>",
      "createdOn" : *************,
      "defaultRole" : "ACCOUNTADMIN",
      "defaultNameSpace" : null,
      "defaultWarehouse" : null,
      "validationState" : "VALIDATED",
      "lastSucLogin" : *************
    }
  },
  "state" : "AUTHN_SUCCESS",
  "response" : {
    "responseDataDTO" : null,
    "ecode" : null,
    "message" : "[********]",
    "messageArgs" : {
      "empty" : true,
      "present" : false
    }
  },
  "authnMethod" : "USERNAME_PASSWORD",
  "authnResInfo" : {
    "isAccessTokenIssuedByOAuthAuthzServer" : false,
    "oauthIntegrationId" : 0,
    "accessTokenSnowflakeRoles" : null,
    "accessTokenRoles" : null,
    "scopedRole" : null,
    "oauthAccessTokenBeWithoutScopes" : false,
    "clientRequestAnyRoles" : false,
    "clientRequestDefaultRole" : false,
    "isAccountActivation" : true,
    "secureTokens" : false,
    "expirationTime" : null,
    "userFriendlyConnectionName" : null,
    "connectionOrg" : null,
    "uiLandingPage" : "SNOWFLAKE_APP",
    "sessionIdForReuse" : null,
    "isRestrictedCallersRightsContext" : false,
    "isAccessTokenWithoutIntegration" : false,
    "isShouldSkipOAuthConsentValidation" : false,
    "assertedClaims" : { },
    "rollingCodeAndVersion" : null,
    "accessTokenWithoutIntegration" : false,
    "restrictedCallersRightsContext" : false,
    "accountActivation" : true,
    "accessTokenIssuedByOAuthAuthzServer" : false,
    "skipOAuthConsentValidation" : false
  },
  "integrationId" : 0,
  "authnEvent" : {
    "clientVersion" : "9.21.0",
    "authnEventId" : ********,
    "externalId" : null,
    "id" : ********,
    "timestamp" : *************,
    "errorCode" : null,
    "errorCodeStr" : null,
    "userName" : "HEWSEEP",
    "authnFactor1Id" : {
      "empty" : true,
      "present" : false
    },
    "typeStr" : "LOGIN",
    "authnFactor1Str" : "PASSWORD",
    "authnFactor2Str" : null,
    "clientIP" : "***************",
    "clientPrivateLinkId" : null,
    "clientTypeStr" : "SNOWFLAKE_UI",
    "authorizingIntegrationName" : ""
  },
  "authnId" : null,
  "redirectURI" : "https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/complete-oauth/snowflake?code=E84F2797F4FB15F824C7F202D650967AD75B9F67",
  "accountName" : "OG38984",
  "submittedAuthnDTO" : null,
  "additionalResponseData" : { },
  "clientIntegration" : {
    "empty" : true,
    "present" : false
  },
  "clientIntegrationId" : -1
},
  "code" : null,
  "message" : null,
  "success" : true
}

curl 'https://browser-intake-datadoghq.com/api/v2/rum?ddsource=browser&ddtags=sdk_version%3A5.27.0%2Capi%3Afetch%2Cenv%3Apublic.aws_ap_northeast_1%2Cservice%3Alogin-ui%2Cversion%3A9.21.0_b2025072603595395e36636&dd-api-key=pub9158139a6f184ed93e7c1730f4a3cd23&dd-evp-origin-version=5.27.0&dd-evp-origin=browser&dd-request-id=1c1e0d65-2798-46cf-9594-b3732abaa896&batch_time=*************' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'Referer: https://ccdiiyc-gx65500.snowflakecomputing.com/' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'Content-Type: text/plain;charset=UTF-8' \
  -H 'sec-ch-ua-mobile: ?0' \
  --data-raw $'{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1},"action":{"target":{"width":336,"height":32,"selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>FORM>DIV.Flex__styles\\\\.common>FIELDSET.Flex__styles\\\\.common>DIV:nth-of-type(1)>DIV.Field__styles\\\\.fieldSetControl>DIV.FieldWrapper__styles\\\\.field"},"position":{"x":99,"y":25}}},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user"},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","in_foreground":true},"action":{"id":"de11def5-0558-48f6-9ba2-c8664045edf7","target":{"name":"Username can contain only letters and numbers."},"type":"click","frustration":{"type":["dead_click"]},"error":{"count":0},"long_task":{"count":0},"resource":{"count":0}},"display":{"viewport":{"width":1141,"height":1284}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"action","context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}\n{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1},"action":{"target":{"width":310,"height":16,"selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>FORM>DIV.Flex__styles\\\\.common>FIELDSET.Flex__styles\\\\.common>DIV:nth-of-type(1)>DIV.Field__styles\\\\.fieldSetControl>DIV.FieldWrapper__styles\\\\.field>INPUT[data-testid=\\"input\\"]"},"position":{"x":110,"y":7}}},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user"},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","in_foreground":true},"action":{"id":"6bbbd4fe-4a88-4ff5-acab-44b63a87b94d","target":{"name":"Username"},"type":"click","loading_time":0,"frustration":{"type":[]},"error":{"count":0},"long_task":{"count":0},"resource":{"count":0}},"display":{"viewport":{"width":1141,"height":1284}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"action","context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}\n{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1},"action":{"target":{"width":310,"height":16,"selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>FORM>DIV.Flex__styles\\\\.common>FIELDSET.Flex__styles\\\\.common>DIV:nth-of-type(2)>DIV.Field__styles\\\\.fieldSetControl>DIV.FieldWrapper__styles\\\\.field>INPUT[data-testid=\\"input\\"]"},"position":{"x":115,"y":7}}},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user"},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","in_foreground":true},"action":{"id":"4aaa06d0-2be9-41db-b7ea-541dc86cd9d5","target":{"name":"Password"},"type":"click","loading_time":0,"frustration":{"type":[]},"error":{"count":0},"long_task":{"count":0},"resource":{"count":0}},"display":{"viewport":{"width":1141,"height":1284}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"action","context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}\n{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1},"action":{"target":{"width":336,"height":296,"selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>FORM>DIV.Flex__styles\\\\.common>FIELDSET.Flex__styles\\\\.common"},"position":{"x":17,"y":220}}},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user"},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","in_foreground":true},"action":{"id":"9d04463c-4309-43f7-9efb-69b3bcb3a156","target":{"name":"Username Username can contain only letters and numbers. Password Your password must be 14 - 256 char [...]"},"type":"click","loading_time":0,"frustration":{"type":[]},"error":{"count":0},"long_task":{"count":0},"resource":{"count":0}},"display":{"viewport":{"width":1141,"height":1284}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"action","context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}\n{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1},"action":{"target":{"width":336,"height":32,"selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>FORM>DIV.Flex__styles\\\\.common>FIELDSET.Flex__styles\\\\.common>DIV:nth-of-type(3)>DIV.Field__styles\\\\.fieldSetControl>DIV.FieldWrapper__styles\\\\.field"},"position":{"x":114,"y":4}}},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user"},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","in_foreground":true},"action":{"id":"********-4f33-4712-abbe-6ceed0b56d40","target":{"name":"Confirm password"},"type":"click","frustration":{"type":["dead_click"]},"error":{"count":0},"long_task":{"count":0},"resource":{"count":0}},"display":{"viewport":{"width":1141,"height":1284}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"action","context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}\n{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1},"action":{"target":{"width":310,"height":16,"selector":"#field-qegehqq"},"position":{"x":93,"y":5}}},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user"},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","in_foreground":true},"action":{"id":"55afce4b-adc6-49a3-8b5a-a2410c4ffa3b","target":{"name":"Confirm password"},"type":"click","loading_time":0,"frustration":{"type":[]},"error":{"count":0},"long_task":{"count":0},"resource":{"count":0}},"display":{"viewport":{"width":1141,"height":1284}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"action","context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}\n{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1},"action":{"target":{"width":310,"height":16,"selector":"#field-qegehqq"},"position":{"x":93,"y":5}}},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user"},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","in_foreground":true},"action":{"id":"96d1cf44-4782-4d92-8c02-47105e29d5cc","target":{"name":"Confirm password"},"type":"click","frustration":{"type":[]},"error":{"count":0},"long_task":{"count":0},"resource":{"count":0}},"display":{"viewport":{"width":1141,"height":1284}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"action","context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}\n{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1},"action":{"target":{"width":336,"height":296,"selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>FORM>DIV.Flex__styles\\\\.common>FIELDSET.Flex__styles\\\\.common"},"position":{"x":211,"y":235}}},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user"},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","in_foreground":true},"action":{"id":"a7a2cb2a-657b-4fa2-b3cc-90652ae95971","target":{"name":"Username Username can contain only letters and numbers. Password Your password must be 14 - 256 char [...]"},"type":"click","loading_time":0,"frustration":{"type":[]},"error":{"count":0},"long_task":{"count":0},"resource":{"count":0}},"display":{"viewport":{"width":1141,"height":1284}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"action","context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}\n{"_dd":{"format_version":2,"drift":0,"configuration":{"session_sample_rate":10,"session_replay_sample_rate":1,"start_session_replay_recording_manually":false},"document_version":5,"page_states":[{"state":"passive","start":**********},{"state":"hidden","start":**********},{"state":"passive","start":***********},{"state":"hidden","start":***********},{"state":"passive","start":***********},{"state":"hidden","start":***********},{"state":"passive","start":***********},{"state":"hidden","start":***********},{"state":"passive","start":***********},{"state":"hidden","start":***********},{"state":"passive","start":************},{"state":"active","start":************}]},"application":{"id":"8b2d9b87-ebd1-4b37-b5c5-6c596fa42899"},"date":*************,"service":"login-ui","version":"9.21.0_b2025072603595395e36636","source":"browser","session":{"id":"98d071a8-eb9c-46e5-8062-6380022f83ac","type":"user","sampled_for_replay":false},"view":{"id":"********-3bec-4f77-818c-96dc71fb6a19","name":"ACCOUNT_ACTIVATION","url":"https://ccdiiyc-gx65500.snowflakecomputing.com/console/login","referrer":"","action":{"count":8},"frustration":{"count":2},"cumulative_layout_shift":0.0028,"cumulative_layout_shift_time":**********,"cumulative_layout_shift_target_selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>DIV.PageCenteredContainer__stylexStyles\\\\.pageHeaderContainerMarginBottom>DIV.PageHeader__styles\\\\.container>P.PageHeader__styles\\\\.description","first_byte":*********,"dom_complete":**********,"dom_content_loaded":**********,"dom_interactive":**********,"error":{"count":1},"first_contentful_paint":**********,"interaction_to_next_paint":8000000,"interaction_to_next_paint_time":************,"interaction_to_next_paint_target_selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>FORM>DIV.Flex__styles\\\\.common>FIELDSET.Flex__styles\\\\.common>DIV:nth-of-type(1)>DIV.Field__styles\\\\.fieldSetControl>DIV.FieldWrapper__styles\\\\.field","is_active":true,"largest_contentful_paint":2240000000,"largest_contentful_paint_target_selector":"#reactRoot>DIV.baltoBaseDimension>DIV.PageCenteredContainer__stylexStyles\\\\.fullPageContainer>DIV.PageCenteredContainer__stylexStyles\\\\.contentOuterContainer>DIV.PageCenteredContainer__stylexStyles\\\\.mainCard>DIV.PageCenteredContainer__stylexStyles\\\\.mainCardContent>FORM>DIV.Flex__styles\\\\.common>FIELDSET.Flex__styles\\\\.common>DIV:nth-of-type(2)>DIV.Field__styles\\\\.fieldSetControl>P.Paragraph__styles\\\\.paragraph","load_event":2551000000,"loading_time":2551000000,"loading_type":"initial_load","long_task":{"count":3},"resource":{"count":12},"time_spent":120901000000},"display":{"viewport":{"width":1141,"height":1284},"scroll":{"max_depth":1284,"max_depth_scroll_top":0,"max_scroll_height":1284,"max_scroll_height_time":3238400000}},"connectivity":{"status":"connected","effective_type":"4g"},"type":"view","feature_flags":{"ENABLE_IDENTIFIER_FIRST_LOGIN":false,"ENABLE_FORGOTTEN_PASSWORD":true,"ENABLE_DUO_V4":true,"ENABLE_FIX_1064101":true,"ENABLE_NEW_MFA_ENGINE_STATE":true,"ENABLE_DARK_MODE":true,"PROVIDED_LOCALES":["ja","en","fr-FR"],"UI_LANDING_PAGE":"SNOWFLAKE_APP","UI_LANDING_PAGE_OVERRIDE":"SNOWFLAKE_APP","LOCALIZE_UI":true,"LOGIN_UI_ENABLE_VIEW_TRANSITION":false,"LOGIN_UI_ENABLE_PROMO_BANNER":true},"privacy":{"replay_level":"mask"},"context":{"account_name":"GX65500","account_locator":"OG38984","organization":"CCDIIYC","authentication_methods":["USERNAME_PASSWORD"],"internal_test_type":null,"locale":"en"}}'

