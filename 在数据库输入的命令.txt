创建数据库

ALTER ACCOUNT SET CORTEX_ENABLED_CROSS_REGION = ‘ANY_REGION’;

/* 0) 以 SECURITYADMIN 登录（或 ACCOUNTADMIN），先捕获当前登录人 */
SET my_user = CURRENT_USER();     -- 例如 'DAV12345'

/* 0.1) 安放安全策略的库 / 模式（可按需修改） */
CREATE DATABASE IF NOT EXISTS SECURITY_DB;
CREATE SCHEMA   IF NOT EXISTS SECURITY_DB.POLICIES;
USE DATABASE SECURITY_DB;
USE SCHEMA   SECURITY_DB.POLICIES;

/* 1) 业务最小权限角色 */
CREATE ROLE IF NOT EXISTS CORTEX_CALLER;
GRANT DATABASE ROLE SNOWFLAKE.CORTEX_USER TO ROLE CORTEX_CALLER;
GRANT ROLE CORTEX_CALLER TO USER IDENTIFIER($my_user);   -- ← 用变量

/* 2) Authentication Policy */
CREATE OR REPLACE AUTHENTICATION POLICY PAT_FLEX
  PAT_POLICY = (NETWORK_POLICY_EVALUATION = ENFORCED_NOT_REQUIRED);

/* 3) 把策略套到当前用户 */
ALTER USER IDENTIFIER($my_user) SET AUTHENTICATION POLICY PAT_FLEX;

/* 4) 生成 30 天有效的 PAT，作用域限定在 CORTEX_CALLER */
ALTER USER IDENTIFIER($my_user) ADD PROGRAMMATIC ACCESS TOKEN CORTEX_PAT
  ROLE_RESTRICTION = 'CORTEX_CALLER'
  DAYS_TO_EXPIRY    = 30;




创建网络规则
USE ROLE ACCOUNTADMIN;
CREATE NETWORK POLICY ALLOW_ALL ALLOWED_IP_LIST=('0.0.0.0/0');

应用网络策略组
ALTER ACCOUNT SET NETWORK_POLICY = ALLOW_ALL;

