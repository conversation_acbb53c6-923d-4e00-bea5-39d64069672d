#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证码解决方案
支持多种验证码服务
"""

import requests
import time
import json

class CaptchaSolver:
    def __init__(self, service='2captcha', api_key=None):
        self.service = service
        self.api_key = api_key
        
        # 服务配置
        self.services = {
            '2captcha': {
                'submit_url': 'http://2captcha.com/in.php',
                'result_url': 'http://2captcha.com/res.php',
                'method': 'userrecaptcha'
            },
            'anticaptcha': {
                'submit_url': 'https://api.anti-captcha.com/createTask',
                'result_url': 'https://api.anti-captcha.com/getTaskResult'
            },
            'rucaptcha': {
                'submit_url': 'http://rucaptcha.com/in.php',
                'result_url': 'http://rucaptcha.com/res.php',
                'method': 'userrecaptcha'
            }
        }
        
    def solve_recaptcha_v2(self, site_key, page_url, proxy=None):
        """解决 reCAPTCHA v2"""
        if self.service == '2captcha':
            return self._solve_2captcha_v2(site_key, page_url, proxy)
        elif self.service == 'anticaptcha':
            return self._solve_anticaptcha_v2(site_key, page_url, proxy)
        elif self.service == 'rucaptcha':
            return self._solve_rucaptcha_v2(site_key, page_url, proxy)
        else:
            raise ValueError(f"不支持的服务: {self.service}")
            
    def _solve_2captcha_v2(self, site_key, page_url, proxy=None):
        """使用 2captcha 解决 reCAPTCHA v2"""
        print("🔄 使用 2captcha 解决验证码...")
        
        # 提交验证码任务
        submit_data = {
            'key': self.api_key,
            'method': 'userrecaptcha',
            'googlekey': site_key,
            'pageurl': page_url,
            'json': 1
        }
        
        if proxy:
            submit_data.update({
                'proxy': proxy,
                'proxytype': 'HTTP'
            })
            
        response = requests.post(self.services['2captcha']['submit_url'], data=submit_data)
        result = response.json()
        
        if result['status'] != 1:
            raise Exception(f"提交验证码失败: {result.get('error_text', 'Unknown error')}")
            
        captcha_id = result['request']
        print(f"✅ 验证码任务已提交，ID: {captcha_id}")
        
        # 等待解决结果
        for attempt in range(60):  # 最多等待5分钟
            time.sleep(5)
            
            result_response = requests.get(self.services['2captcha']['result_url'], params={
                'key': self.api_key,
                'action': 'get',
                'id': captcha_id,
                'json': 1
            })
            
            result = result_response.json()
            
            if result['status'] == 1:
                print("✅ 验证码解决成功")
                return result['request']
            elif result['error_text'] == 'CAPCHA_NOT_READY':
                print(f"⏳ 等待验证码解决... ({attempt + 1}/60)")
                continue
            else:
                raise Exception(f"验证码解决失败: {result.get('error_text', 'Unknown error')}")
                
        raise Exception("验证码解决超时")
        
    def _solve_anticaptcha_v2(self, site_key, page_url, proxy=None):
        """使用 anticaptcha 解决 reCAPTCHA v2"""
        print("🔄 使用 anticaptcha 解决验证码...")
        
        # 构建任务数据
        task_data = {
            "clientKey": self.api_key,
            "task": {
                "type": "NoCaptchaTaskProxyless",
                "websiteURL": page_url,
                "websiteKey": site_key
            }
        }
        
        if proxy:
            task_data["task"]["type"] = "NoCaptchaTask"
            task_data["task"]["proxyType"] = "http"
            task_data["task"]["proxyAddress"] = proxy.split(':')[0]
            task_data["task"]["proxyPort"] = int(proxy.split(':')[1])
            
        # 提交任务
        response = requests.post(self.services['anticaptcha']['submit_url'], json=task_data)
        result = response.json()
        
        if result.get('errorId') != 0:
            raise Exception(f"提交验证码失败: {result.get('errorDescription', 'Unknown error')}")
            
        task_id = result['taskId']
        print(f"✅ 验证码任务已提交，ID: {task_id}")
        
        # 等待解决结果
        for attempt in range(60):
            time.sleep(5)
            
            result_response = requests.post(self.services['anticaptcha']['result_url'], json={
                "clientKey": self.api_key,
                "taskId": task_id
            })
            
            result = result_response.json()
            
            if result.get('status') == 'ready':
                print("✅ 验证码解决成功")
                return result['solution']['gRecaptchaResponse']
            elif result.get('status') == 'processing':
                print(f"⏳ 等待验证码解决... ({attempt + 1}/60)")
                continue
            else:
                raise Exception(f"验证码解决失败: {result.get('errorDescription', 'Unknown error')}")
                
        raise Exception("验证码解决超时")
        
    def _solve_rucaptcha_v2(self, site_key, page_url, proxy=None):
        """使用 rucaptcha 解决 reCAPTCHA v2"""
        print("🔄 使用 rucaptcha 解决验证码...")
        
        # 提交验证码任务
        submit_data = {
            'key': self.api_key,
            'method': 'userrecaptcha',
            'googlekey': site_key,
            'pageurl': page_url,
            'json': 1
        }
        
        if proxy:
            submit_data.update({
                'proxy': proxy,
                'proxytype': 'HTTP'
            })
            
        response = requests.post(self.services['rucaptcha']['submit_url'], data=submit_data)
        result = response.json()
        
        if result['status'] != 1:
            raise Exception(f"提交验证码失败: {result.get('error_text', 'Unknown error')}")
            
        captcha_id = result['request']
        print(f"✅ 验证码任务已提交，ID: {captcha_id}")
        
        # 等待解决结果
        for attempt in range(60):
            time.sleep(5)
            
            result_response = requests.get(self.services['rucaptcha']['result_url'], params={
                'key': self.api_key,
                'action': 'get',
                'id': captcha_id,
                'json': 1
            })
            
            result = result_response.json()
            
            if result['status'] == 1:
                print("✅ 验证码解决成功")
                return result['request']
            elif result['error_text'] == 'CAPCHA_NOT_READY':
                print(f"⏳ 等待验证码解决... ({attempt + 1}/60)")
                continue
            else:
                raise Exception(f"验证码解决失败: {result.get('error_text', 'Unknown error')}")
                
        raise Exception("验证码解决超时")

def get_demo_recaptcha_token():
    """获取演示用的 reCAPTCHA token"""
    # 这是从实际请求中提取的示例 token
    return "03AFcWeA4OCNEPkvtATYf3sybaVKs0Ov-073ftksLy1PXZs_yK8LAoOZ59h9R25pdApiKQHy_YEeUhCIBMjTGyJKemQzKi0Nk2pB5wKVlWfhIxL9uxd-F9B5AU0eaTd6jJGiqVMiiNv1RkcGd0mAGC-dGU7Ozat8iugWZ65hnAeRP_Y9OnvR6rqCQ-FCyuBKfwiaeiltO1FkFUI0EpkDld-MUtbkDNHr98ERQqZiz6G_fyFrmSMZFgvZRVnXhrMJqiDGBvwZlNzaJn2cQ3c0FEtu_POWlCefWO7lI_v4YR0RD9ltXkTntIcK9zof9xq_clNkVaIUrshT9U5m_jJ34qA_Mwzadk323uOG_eAnfbPYaSpgoT7O7wwsuCVGGLOjGXPuUw5Hk1CB4r01iDyxcZoEkMop4U2d8zfAjrglNfuj07StyfOZQ8_EodMhSLHftV9F6v8ex4gdbzsFfzXcWwX5ZFTTaXvDjm-KMTHTs39_dCNecA9WEC0qHfXAR0vgL14CIxxknIXHvYo1az2WJKcSzsIWAw929ml_nH43NQZEEL-fNr1F1R-LCIILDWyJ-JYG2OHhJDVFtmk4cvXlWyIW6UghkX1uQBIVoWVQ0Lft091mOD1J1fv1eZhvIfWTV73N7VwhbnDn50tvETeinBAHDdkg1Z7Lv6dTfxJY2nXfBLm3S96B6fUZjEHislPgayQD1jTqfij4w-m3GEmKn4r-uMec3aYp60DMgnjocvi3nqH_BWhOg03mfzjAICcCah3ov7OuBOSVOjTe3JnMGhexxbntFhKiAxd5JlfVhI1RMFmKAG1SfUrE6SKbZJKzCYR1HWAzFu1HuXi0cIqF-YELknjyOo_l_MN26A9C5nK3rHbG8VP5HRna72BHxhbMRgcYjJ622LFbnDXo6W62hU5KvJoAwxNqdlRoGj2B1ZPEExY-MVrFYlWIkQTCEEBG0eJPbipCnOZtyt"

if __name__ == "__main__":
    # 测试验证码解决
    solver = CaptchaSolver('2captcha', 'your_api_key_here')
    
    # Snowflake 的 reCAPTCHA 配置
    site_key = "6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV"
    page_url = "https://signup.snowflake.com/"
    
    try:
        token = solver.solve_recaptcha_v2(site_key, page_url)
        print(f"验证码解决成功: {token[:50]}...")
    except Exception as e:
        print(f"验证码解决失败: {e}")
        print("使用演示 token...")
        token = get_demo_recaptcha_token()
        print(f"演示 token: {token[:50]}...")
