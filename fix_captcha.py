#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复验证码解决逻辑，只使用 YesCaptcha，增加等待时间
"""

def fix_captcha_logic():
    # 读取原文件
    with open('snowflake_register.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到 solve_recaptcha 方法的开始和结束
    lines = content.split('\n')
    new_lines = []
    in_solve_method = False
    method_indent = 0
    
    i = 0
    while i < len(lines):
        line = lines[i]
        
        if 'def solve_recaptcha(self):' in line:
            in_solve_method = True
            method_indent = len(line) - len(line.lstrip())
            new_lines.append(line)
        elif in_solve_method and line.strip() and len(line) - len(line.lstrip()) <= method_indent and not line.startswith(' ' * (method_indent + 4)):
            # 方法结束
            in_solve_method = False
            new_lines.append(line)
        elif in_solve_method:
            # 在方法内部，进行修改
            if 'for attempt in range(60):  # 最多等待5分钟' in line:
                new_lines.append(line.replace('range(60)', 'range(120)').replace('5分钟', '10分钟'))
            elif '验证码解决中... ({attempt + 1}/60)' in line:
                new_lines.append(line.replace('/60)', '/120)'))
            elif '# 如果 YesCaptcha 失败，使用备用 token' in line:
                # 替换备用 token 逻辑
                new_lines.append('        # YesCaptcha 失败时返回 None')
                new_lines.append('        return None')
                # 跳过后续的备用 token 代码
                while i + 1 < len(lines) and (lines[i + 1].startswith('        ') or lines[i + 1].strip() == ''):
                    i += 1
            else:
                new_lines.append(line)
        else:
            new_lines.append(line)
        
        i += 1
    
    # 写回文件
    with open('snowflake_register.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print('✅ 验证码逻辑修复完成')

if __name__ == "__main__":
    fix_captcha_logic()
