curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v0/queries' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: traceparent,x-csrf-token,x-numeracy-client-version,x-numeracy-userid,x-snowflake-context,x-snowflake-context-encoded,x-snowflake-page-source,x-snowflake-request-id,x-snowflake-role,x-snowflake-role-encoded' \
  -H 'access-control-request-method: POST' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'

curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v0/queries' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/x-www-form-urlencoded' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-000000000000000000c877d29408c075-24e76813404c1644-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-csrf-token: 790aaad3' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-numeracy-userid: *************' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-page-source: worksheets' \
  -H 'x-snowflake-request-id: 7659edc7-817d-4299-8fee-b8cab48b3786' \
  -H 'x-snowflake-role: ACCOUNTADMIN' \
  -H 'x-snowflake-role-encoded: 1#ACCOUNTADMIN' \
  --data-raw 'folder=%7B%22id%22%3A%22worksheets%22%2C%22orgId%22%3A%***************%22%2C%22type%22%3A%22list%22%2C%22ownerId%22%3A%22*************%22%2C%22name%22%3A%22Worksheets%22%2C%22slug%22%3A%22worksheets%22%2C%22url%22%3A%22%2Fccdiiyc%2Fgx65500%2Fworksheets%22%2C%22editable%22%3Atrue%2C%22_virtual%22%3Atrue%7D&queryLanguage=sql&action=create&orgId=*************&name=2025-08-01%202%3A13pm&executionContext=%7B%22role%22%3A%22ACCOUNTADMIN%22%7D'

{
    "models": {
        "queries": {
            "3tG8cGGSvgs": {
                "snowflakeRequestId": "e26a8afb-3cd1-4458-b933-cd8f6e0224e3",
                "snowflakeQueryId": "",
                "queryContext": {
                    "role": "ACCOUNTADMIN",
                    "warehouse": "",
                    "database": "",
                    "schema": "",
                    "secondaryRoles": "ALL"
                },
                "startDate": "2025-08-01T06:13:49.259321683Z",
                "transforms": [],
                "queryLanguage": "sql",
                "pid": "3tG8cGGSvgs",
                "name": "2025-08-01 2:13pm",
                "orgId": "*************",
                "ownerId": "*************",
                "folderId": null,
                "visibility": "private",
                "modified": "2025-08-01T06:13:49.259321Z",
                "version": 1,
                "isParamQuery": false,
                "projectType": "query",
                "executionContext": {
                    "role": "ACCOUNTADMIN",
                    "warehouse": "",
                    "database": "",
                    "schema": "",
                    "secondaryRoles": "ALL"
                },
                "editable": true,
                "runnable": true,
                "resultsViewable": true,
                "url": "/ccdiiyc/gx65500/w3tG8cGGSvgs#query",
                "slug": "w3tG8cGGSvgs",
                "members": [
                    {
                        "memberType": "user",
                        "userId": "*************",
                        "memberId": "*************",
                        "role": "owner",
                        "hasRole": true
                    }
                ],
                "hasRequiredRole": true
            }
        },
        "queryResults": {
            "3tG8cGGSvgs": {
                "populated": false,
                "ready": false,
                "canceled": false,
                "rewritten": false,
                "cappedRows": 0,
                "hasSession": false,
                "resumeStarted": "0001-01-01T00:00:00Z",
                "origJobId": "00000000-0000-0000-0000-000000000000",
                "sheet": null,
                "modified": "2025-08-01T06:13:49.259321Z",
                "version": 1,
                "error": null,
                "snowflakeQueryId": "",
                "snowflakeRequestId": "e26a8afb-3cd1-4458-b933-cd8f6e0224e3"
            }
        },
        "queryPrefs": {
            "3tG8cGGSvgs": {
                "schemaCards": {}
            }
        },
        "dbSchemas": {},
        "worksheetImports": {},
        "users": {
            "*************": {
                "id": *************,
                "name": "rwe rwqe",
                "email": "<EMAIL>",
                "firstName": "rwe",
                "lastName": "rwqe",
                "canShare": true,
                "canModifyParameters": true,
                "modifyParameterRoles": [
                    "ACCOUNTADMIN"
                ],
                "defaultOrgId": "*************",
                "settings": {
                    "seenHints": {
                        "intro": false,
                        "inspectorSelection": false,
                        "inspectorHistogram": false,
                        "inspectorCategoryFilter": false,
                        "inspectorQualityMeter": false,
                        "queryLimit": false,
                        "recencyMessage": false
                    },
                    "notifications": {},
                    "snowflakeTokenId": "*************",
                    "lastUsedQueryContext": {
                        "role": "",
                        "warehouse": "",
                        "database": "",
                        "schema": "",
                        "secondaryRoles": ""
                    },
                    "snowflakeFeatures": {
                        "ALLOW_ENABLING_AUTO_RENEWALS_FOR_PUBLISHED_MONETIZED_LISTINGS": false,
                        "ALLOW_HIGH_MEMORY_WAREHOUSE": true,
                        "ALLOW_SET_WAREHOUSE_RESOURCE_CONSTRAINT_MEMORY_16X": true,
                        "ALLOW_SET_WAREHOUSE_RESOURCE_CONSTRAINT_MEMORY_16X_X86": true,
                        "ALLOW_SET_WAREHOUSE_RESOURCE_CONSTRAINT_MEMORY_1X": true,
                        "ALLOW_SET_WAREHOUSE_RESOURCE_CONSTRAINT_MEMORY_1X_X86": true,
                        "ALLOW_SET_WAREHOUSE_RESOURCE_CONSTRAINT_MEMORY_64X": true,
                        "ALLOW_SET_WAREHOUSE_RESOURCE_CONSTRAINT_MEMORY_64X_X86": true,
                        "BLOCK_PERSONALIZED_LISTINGS": true,
                        "BUDGETS_DATABASE": "",
                        "COMMUNITY_BASE_URL": "https://community.snowflake.com",
                        "CORTEX_AI_DATA_RETENTION_POLICY": "ALWAYS",
                        "CORTEX_ENABLED_CROSS_REGION": "DISABLED",
                        "CUSTOM_CLIENT_VERSION_PINNING": "",
                        "DATA_EXCHANGE_PRIVATE_LISTING_PUBLISH_LIMIT": 200,
                        "DBT_ALLOWED_COMMANDS": "run,compile,test,show,build,list,ls,snapshot,parse,run-operation,deps,seed,debug,source",
                        "DEVELOPERS_BASE_URL": "https://developers.snowflake.com",
                        "DISABLE_MFA_ENROLLMENT_PROMPT": true,
                        "DISABLE_SELF_PASSWORD_CHANGE": false,
                        "DISABLE_UI_DOWNLOAD_BUTTON": false,
                        "DMX_DEFAULT_OFFER_NAME_FOR_MIGRATION": "MIGRATED_PRICING_PLAN_DEFAULT",
                        "DMX_DEFAULT_PRICING_PLAN_NAME_FOR_MIGRATION": "MIGRATED_PRICING_PLAN",
                        "DOCS_BASE_URL": "https://docs.snowflake.com",
                        "DOCUMENT_AI_VERSION": "{\"current\": \"stable\", \"fallback\": \"stable\"}",
                        "ENABLE_ACCOUNT_DATABASE_REPLICATION": false,
                        "ENABLE_ADAPTIVE_SOW_WAREHOUSES": false,
                        "ENABLE_ADMIN_UNPUBLISH_LISTINGS_FOR_PRIVATE_DATA_EXCHANGE": false,
                        "ENABLE_ALLOW_PURCHASE_WITHOUT_CC_ON_FILE": false,
                        "ENABLE_APPLICATION_OBJECTS_CONSUMER_INTEGRATION": true,
                        "ENABLE_APPLICATION_OBJECTS_MONETIZATION": true,
                        "ENABLE_CONSUMER_TRIAL_DETAILS_IN_LISTING_API": true,
                        "ENABLE_CORTEX_ANALYST": true,
                        "ENABLE_DATA_DICTIONARY_FOR_PRIVATE_LISTINGS_ON_PROVIDER": true,
                        "ENABLE_DATA_DICTIONARY_ON_PROVIDER": true,
                        "ENABLE_DATA_PREVIEW_ON_ADMIN": true,
                        "ENABLE_DATA_PREVIEW_ON_CONSUMER": true,
                        "ENABLE_DATA_PREVIEW_ON_PROVIDER": true,
                        "ENABLE_DATA_PREVIEW_WITH_PII": true,
                        "ENABLE_DBT_HISTORY_REST_ENDPOINT": true,
                        "ENABLE_DBT_UI": true,
                        "ENABLE_DEFAULT_OFFER_RETIREMENT": false,
                        "ENABLE_DMX_CONSUMER_GOOD_BETTER_BEST": true,
                        "ENABLE_DMX_CONSUMER_OFFERS_AND_PRICING_PLANS": true,
                        "ENABLE_DMX_MONETIZATION_IS_MONETIZED_COLUMN": true,
                        "ENABLE_DMX_PROVIDER_GOOD_BETTER_BEST": true,
                        "ENABLE_DMX_PROVIDER_OFFERS_AND_PRICING_PLANS": false,
                        "ENABLE_DMX_SPCS_SURCHARGE": false,
                        "ENABLE_DMX_USAGE_BASED_PLANS_CHANGE_TO_DAYS_FROM_CURRENT": false,
                        "ENABLE_DUO_V4": true,
                        "ENABLE_EGRESS_COST_OPTIMIZER_COMPLIANCE_CHECK": true,
                        "ENABLE_FEATURE_CONSUMER_SEPARATE_PRICING_PLAN_AND_PUBLIC_OFFER_CREATION": false,
                        "ENABLE_FEATURE_PERSONAL_DATABASE": true,
                        "ENABLE_FEATURE_PROVIDER_SEPARATE_PRICING_PLAN_AND_PUBLIC_OFFER_CREATION": false,
                        "ENABLE_FIX_11488391_ALLOW_WAREHOUSE_IN_SHOW_STREAMLIT": false,
                        "ENABLE_FIX_1209797_CUTOFF_TIME_FOR_RETIREMENT_WINDOW_CHANGE": 1714521600000,
                        "ENABLE_FIX_1983774_BLOCK_USAGE_BASED_TRIALS": true,
                        "ENABLE_FIX_954145_ENABLE_APP_LISTING_VISIBILITY_IN_AZURE": true,
                        "ENABLE_FIX_ALLOW_WRITE_USAGE_PRIVILEGE_TO_REFRESH_STAGE": true,
                        "ENABLE_GLOBAL_OBJECT_FAILOVER_CAPABILITY": false,
                        "ENABLE_LISTING_AUTO_FULFILLMENT_APP_LISTING_REPLICATION": true,
                        "ENABLE_LISTING_AUTO_FULFILLMENT_FULFILLER": false,
                        "ENABLE_LISTING_AUTO_FULFILLMENT_REFERENCE_USAGE_REPLICATION": false,
                        "ENABLE_LISTING_RETIREMENT": true,
                        "ENABLE_MARKETPLACE_FOR_VPS": true,
                        "ENABLE_MFA_DELETE_FROM_UI": true,
                        "ENABLE_MODIFY_MFA_METHOD": true,
                        "ENABLE_MONETIZATION_SPCS_TIME_BASED_SURCHARGE": false,
                        "ENABLE_MONETIZED_LISTING_REGION_REMOVAL": false,
                        "ENABLE_MULTIPLE_INSTALLATIONS_FROM_THE_SAME_LISTING": null,
                        "ENABLE_NATIVE_APP_BILLING_EVENT": true,
                        "ENABLE_NOTEBOOK_CREATION_IN_PERSONAL_DB": false,
                        "ENABLE_NOTEBOOK_UNIFIED_LIFECYCLE_ERROR_MSG": true,
                        "ENABLE_PAYMENT_METHOD_SELECTION": true,
                        "ENABLE_PERSONAL_DATABASE": true,
                        "ENABLE_PRIVATE_OFFER_SUBSCRIPTIONS": true,
                        "ENABLE_PROVIDER_TRIAL_DETAILS_IN_LISTING_API": true,
                        "ENABLE_SEARCH_FOR_NEW_DOMAIN": false,
                        "ENABLE_SHARING_SEMANTIC_VIEW": false,
                        "ENABLE_SHOW_MANAGED_ACCOUNTS_NEW_COLUMN_NAME": true,
                        "ENABLE_SHOW_WAREHOUSE_RESOURCE_CONSTRAINT": true,
                        "ENABLE_STREAMLIT_SPCS_RUNTIME_V2": false,
                        "ENABLE_STREAMLIT_USE_V1_SETUP_FOR_V2": null,
                        "ENABLE_STREAMLIT_VERSIONED_STAGE": true,
                        "ENABLE_SUPPORT_USER_LINKAGE": true,
                        "ENABLE_SYSTEM_FUNCTION_GET_ROLES_WITH_MARKETPLACE_PRIVILEGE": true,
                        "ENABLE_TARGETING_ALL_INTERNAL_ACCOUNTS_IN_ORGANIZATION_TARGETS": true,
                        "ENABLE_TIP_DELIVERY_ENDPOINS_IN_COLDBREW": true,
                        "ENABLE_TRIAL_DETAILS_IN_LISTING_API": true,
                        "ENABLE_UBAC_COMMANDS": true,
                        "ENABLE_UI_FEATURE_NOTEBOOKS_ACTIVE_STATUS": false,
                        "ENABLE_USER_DEFAULT_MFA_METHOD": true,
                        "ENABLE_USER_TASKS_SHOW_MODE_IN_SHOW_DESC": true,
                        "ENABLE_USER_TASKS_SHOW_TARGET_COMPLETION_INTERVAL_IN_SHOW_DESC": true,
                        "ENFORCE_SESSION_POLICY": true,
                        "EXEMPT_LEGACY_SERVICE_DEPRECATION": false,
                        "EXEMPT_MFA_ENFORCEMENT_HUMAN_USERS": false,
                        "FEATURE_ALLOW_SET_WAREHOUSE_HARDWARE_GENERATION_RESOURCE_CONSTRAINT": "ENABLED",
                        "FEATURE_COPILOT_FULL_PAGE": "DISABLED",
                        "FEATURE_COPILOT_HOME": "DISABLED",
                        "FEATURE_COPILOT_MARKETPLACE_LISTING": "DISABLED",
                        "FEATURE_COPILOT_NOTEBOOK": "DISABLED",
                        "FEATURE_COPILOT_STREAMLIT": "DISABLED",
                        "FEATURE_COPILOT_WORKSHEET": "ENABLED",
                        "FEATURE_CORTEX_ANALYST_ADMIN_UI": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_CORTEX_ANALYST_ADMIN_UI_AUTO_GENERATE_SEMANTIC_MODEL": "DISABLED",
                        "FEATURE_ENABLE_ADAPTIVE": "DISABLED_PRIVATE_PREVIEW",
                        "FEATURE_INTRA_ORG": "ENABLED",
                        "FEATURE_MARKETPLACE_COPILOT_WITH_SEMANTIC_VIEWS": "DISABLED_PRIVATE_PREVIEW",
                        "FEATURE_SNOWGIT_OAUTH_SUPPORT": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_DEX_27": "DISABLED",
                        "FEATURE_UI_DEX_28": "DISABLED",
                        "FEATURE_UI_DEX_29": "DISABLED",
                        "FEATURE_UI_DEX_30": "DISABLED",
                        "FEATURE_UI_DEX_31": "DISABLED",
                        "FEATURE_UI_DEX_32": "DISABLED",
                        "FEATURE_UI_DEX_33": "DISABLED",
                        "FEATURE_UI_DEX_34": "DISABLED",
                        "FEATURE_UI_DEX_35": "DISABLED",
                        "FEATURE_UI_DEX_37": "ENABLED",
                        "FEATURE_UI_DEX_38": "DISABLED",
                        "FEATURE_UI_DEX_39": "DISABLED",
                        "FEATURE_UI_DEX_40": "DISABLED",
                        "FEATURE_UI_DEX_41": "DISABLED",
                        "FEATURE_UI_DEX_42": "DISABLED",
                        "FEATURE_UI_DEX_43": "DISABLED",
                        "FEATURE_UI_DEX_44": "DISABLED",
                        "FEATURE_UI_DEX_45": "ENABLED",
                        "FEATURE_UI_DEX_46": "DISABLED",
                        "FEATURE_UI_DEX_47": "DISABLED",
                        "FEATURE_UI_DEX_48": "DISABLED",
                        "FEATURE_UI_DEX_49": "DISABLED",
                        "FEATURE_UI_DEX_50": "DISABLED",
                        "FEATURE_UI_DEX_51": "DISABLED",
                        "FEATURE_UI_DEX_52": "DISABLED",
                        "FEATURE_UI_DEX_53": "DISABLED",
                        "FEATURE_UI_DEX_54": "DISABLED",
                        "FEATURE_UI_DEX_55": "DISABLED",
                        "FEATURE_UI_DEX_56": "DISABLED",
                        "FEATURE_UI_DEX_57": "DISABLED",
                        "FEATURE_UI_DEX_58": "DISABLED",
                        "FEATURE_UI_DEX_59": "DISABLED",
                        "FEATURE_UI_DEX_60": "DISABLED",
                        "FEATURE_UI_ENABLE_ACCOUNT_REPLICATION_BULK_FAILOVER": "DISABLED",
                        "FEATURE_UI_ENABLE_ACCOUNT_TASKS_GRAPH_HISTORY_TAB": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_ACCOUNT_TASKS_GRAPH_OVERVIEW": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_ALERTS_OBSERVABILITY": "DISABLED",
                        "FEATURE_UI_ENABLE_ALERTS_OBSERVABILITY_PREVIEW_PILL": "DISABLED",
                        "FEATURE_UI_ENABLE_CONSISTENT_LAYOUT": "DISABLED",
                        "FEATURE_UI_ENABLE_COPY_HISTORY_NEW_LAYOUT": "ENABLED",
                        "FEATURE_UI_ENABLE_COPY_HISTORY_NEW_QUERY_APPROACH": "DISABLED",
                        "FEATURE_UI_ENABLE_CORTEX_SEARCH_PAGE": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_CREATE_EDIT_GIT_REPOSITORY": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_CREATE_ICEBERG_TABLE": "DISABLED",
                        "FEATURE_UI_ENABLE_CREATE_NOTIFICATION_INTEGRATION": "DISABLED",
                        "FEATURE_UI_ENABLE_CREATE_PIPE": "DISABLED",
                        "FEATURE_UI_ENABLE_CREATE_STORAGE_INTEGRATION": "DISABLED",
                        "FEATURE_UI_ENABLE_DNA_LISTING_CONSUMER_FLOW": "ENABLED",
                        "FEATURE_UI_ENABLE_DNA_LISTING_PROVIDER_FLOW": "ENABLED",
                        "FEATURE_UI_ENABLE_DYNAMIC_TABLES_BASE_TABLES_CHANGED": "DISABLED",
                        "FEATURE_UI_ENABLE_DYNAMIC_TABLE_LAG_CHART": "DISABLED",
                        "FEATURE_UI_ENABLE_EXPERIMENT_TRACKING": "DISABLED",
                        "FEATURE_UI_ENABLE_FEATURE_STORE": "ENABLED",
                        "FEATURE_UI_ENABLE_FETCH_SECONDARY_REPLICATION_DATA": "ENABLED",
                        "FEATURE_UI_ENABLE_FINETUNING": "DISABLED",
                        "FEATURE_UI_ENABLE_GIT_INTEGRATION_OAUTH": "DISABLED",
                        "FEATURE_UI_ENABLE_GIT_REPOSITORIES": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_GIT_REPOSITORY_FILES_DOWNLOAD": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_GIT_REPOSITORY_FILES_EXECUTE_IMMEDIATE": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_GIT_REPOSITORY_FILES_EXPLORER": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_INTEGRATIONS_OBSERVABILITY": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_INTRA_ORG_LISTING_CONTACTS_SECTION": "ENABLED",
                        "FEATURE_UI_ENABLE_INTRA_ORG_LISTING_REQUEST_APPROVAL": "ENABLED",
                        "FEATURE_UI_ENABLE_LISTING_IMAGES_CONSUMER": "DISABLED",
                        "FEATURE_UI_ENABLE_LISTING_IMAGES_PROVIDER": "DISABLED",
                        "FEATURE_UI_ENABLE_LLM_OBSERVABILITY": "ENABLED",
                        "FEATURE_UI_ENABLE_LLM_PLAYGROUND": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_LOAD_FILE_DATA_PREVIEW": "ENABLED",
                        "FEATURE_UI_ENABLE_LOG_EXPLORER_NA_FILTERS": "ENABLED",
                        "FEATURE_UI_ENABLE_LOG_EXPLORER_SPCS_FILTERS": "ENABLED",
                        "FEATURE_UI_ENABLE_M2_NEW_PROVIDER_ANALYTICS_IA": "ENABLED",
                        "FEATURE_UI_ENABLE_ML_FUNCTIONS": "ENABLED",
                        "FEATURE_UI_ENABLE_MODEL_REGISTRY": "ENABLED",
                        "FEATURE_UI_ENABLE_MODEL_SERVING": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_NEW_PROVIDER_ANALYTICS_IA": "DISABLED",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_CHANGELIST": "ENABLED",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_CODE_DIFF": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_CONFLICTS_RESOLUTION": "DISABLED",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_DUPLICATION": "ENABLED",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_FILE_BROWSER": "ENABLED",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_FILE_PREVIEW": "ENABLED",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_SCHEDULING": "ENABLED",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_SCHEDULING_ON_SPCS": "enabled",
                        "FEATURE_UI_ENABLE_NOTEBOOKS_UPLOAD_DOWNLOAD": "ENABLED",
                        "FEATURE_UI_ENABLE_OBJECT_DEPENDENCIES_GRAPH": "DISABLED",
                        "FEATURE_UI_ENABLE_OBJECT_TASK_GRAPHS_HISTORY": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_OBSERVABILITY_MONITORING": "ENABLED",
                        "FEATURE_UI_ENABLE_PIPE_SINGLE_OBJECT_COPY_HISTORY": "ENABLED",
                        "FEATURE_UI_ENABLE_PIPE_SINGLE_OBJECT_COPY_HISTORY_GRAPHS": "ENABLED",
                        "FEATURE_UI_ENABLE_PIPE_SINGLE_OBJECT_GRAPH": "ENABLED",
                        "FEATURE_UI_ENABLE_PIPE_SINGLE_OBJECT_MANUAL_REFRESH": "ENABLED",
                        "FEATURE_UI_ENABLE_PROVIDER_RESHARING_PRPR": "disabled",
                        "FEATURE_UI_ENABLE_QUERY_HISTORY_TELEMETRY": "ENABLED",
                        "FEATURE_UI_ENABLE_SEARCH_SERVICE": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_SNOWSERVICES_USER_FACING_FEATURES": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_ENABLE_SPCS_NAX": "ENABLED",
                        "FEATURE_UI_ENABLE_STREAMLIT_GIT_SYNC": "ENABLED",
                        "FEATURE_UI_ENABLE_STREAMLIT_MULTIFILE": "ENABLED",
                        "FEATURE_UI_ENABLE_STREAMLIT_SCHEDULING": "DISABLED",
                        "FEATURE_UI_ENABLE_SYNTHETIC_DATA_GENERATION": "DISABLED",
                        "FEATURE_UI_ENABLE_TASKS_PROCEDURES_DEBUGGING": "DISABLED_PRIVATE_PREVIEW",
                        "FEATURE_UI_ENABLE_TASK_RE_RUN_ACTION": "ENABLED",
                        "FEATURE_UI_ENABLE_TASK_RE_RUN_ATTEMPTS_VISIBILITY": "ENABLED",
                        "FEATURE_UI_ENABLE_TASK_RUN_STATUS_AUTO_REFRESH": "DISABLED_PRIVATE_PREVIEW",
                        "FEATURE_UI_ENABLE_TELEMETRY_EXPLORER_PAGE": "ENABLED",
                        "FEATURE_UI_ENABLE_USER_SETTINGS": "ENABLED",
                        "FEATURE_UI_ENABLE_USER_SETTINGS_MFA": "ENABLED",
                        "FEATURE_UI_ENABLE_USER_SETTINGS_PROFILE_AND_PREFERENCES": "ENABLED",
                        "FEATURE_UI_ENABLE_USER_SETTINGS_PROGRAMMATIC_ACCESS_TOKEN": "ENABLED",
                        "FEATURE_UI_ENABLE_WAREHOUSE_UTILIZATION": "DISABLED",
                        "FEATURE_UI_INTRA_ORG_PUBLISHING_FLOW": "ENABLED",
                        "FEATURE_UI_ORGANIZATION_PROVIDER_PROFILES": "ENABLED_PUBLIC_PREVIEW",
                        "FEATURE_UI_READER_ACCOUNTS_IN_ADMIN": "ENABLED",
                        "FEATURE_UI_WYSIWYG_LISTING_PUBLISHING_FLOW": "ENABLED",
                        "FOLDER_ROLE_MIGRATION": true,
                        "GLOBAL_ENABLE_NEW_CONNECTION_SYNTAX": false,
                        "GLOBAL_ENABLE_USER_FRIENDLY_URLS": false,
                        "GLOBAL_REPLICATION_ENABLE_CONNECTION_OBJECTS": false,
                        "INFO_SCHEMA_COMPLETE_TASK_GRAPHS_FUNCTION_VERSION": 5,
                        "INFO_SCHEMA_CURRENT_TASK_GRAPHS_FUNCTION_VERSION": 5,
                        "INFO_SCHEMA_TASK_HISTORY_FUNCTION_VERSION": 8,
                        "INGEST_MINIMUM_TIMESTAMP_COPY_HISTORY_SCHEMA_DATA": "2024-03-19T00:00:00z",
                        "LANGUAGE": "en",
                        "LARGE_WAREHOUSE_CLUSTER_COUNT_LIMIT": 160,
                        "LISTING_AUTOFULFILLMENT_INITIAL_REPLICATION_SIZE_LIMIT_IN_TB": 10,
                        "LISTING_AUTO_FULFILLMENT_REPLICATION_REFRESH_SCHEDULE": "1440 MINUTE",
                        "LISTING_MAX_CATEGORIES_COUNT": 3,
                        "MANAGED_ACCOUNT_ACTIVE_MAX_COUNT": 75,
                        "MARKETPLACE_FOOTER_VARIANT": "default",
                        "MAX_APPLICATION_INSTANCES_PER_LISTING": 30,
                        "MEDIUM_WAREHOUSE_CLUSTER_COUNT_LIMIT": 300,
                        "MONETIZATION_CONSUMER_ALLOWED_COUNTRIES": "AE,AT,AU,BE,BM,CA,CH,CO,CZ,DE,DK,FI,FR,GB,IE,IL,IN,IT,JP,KR,KY,LU,MX,NL,NO,NZ,PL,PT,SE,SG,US",
                        "MONETIZATION_MAX_OFFERS_SIZE": 100,
                        "MONETIZATION_MAX_PRICING_PLANS_SIZE": 100,
                        "MONETIZATION_MAX_VISIBLE_PRICING_PLANS_SIZE": 9,
                        "MULTI_CLUSTER_WAREHOUSES": true,
                        "NATIVE_APP_BILLING_EVENT_MAX_BASE_CHARGE": 99999.99,
                        "NONINTERACTIVE_NOTEBOOK_RESULT_PURGE_TIME": 2592000,
                        "NOTEBOOK_CACHE_FETCHING_TIMEOUT_SECONDS": 10,
                        "NOTEBOOK_CURRENT_RELEASE_VERSION": "nb-0.0.1",
                        "NOTEBOOK_DEFAULT_RUNTIME": "warehouse:WH-RUNTIME-2.0",
                        "NOTEBOOK_ENABLE_GS_IFRAME_IN_DEV": false,
                        "NOTEBOOK_ENABLE_IDLE_HEARTBEAT": true,
                        "NOTEBOOK_ENABLE_RUNTIMES": true,
                        "NOTEBOOK_ENABLE_RUNTIME_SELECTION": true,
                        "NOTEBOOK_IDLE_HEARTBEAT_TIMEOUT_SECONDS": 3600,
                        "NOTEBOOK_IFRAME_LOAD_RANGE_BUFFER": 6,
                        "NOTEBOOK_IFRAME_LOAD_TRIGGER_BUFFER": 3,
                        "NOTEBOOK_PREVIEW_RUNTIME_ENVIRONMENT_VERSION": "warehouse:WH-RUNTIME-1.0",
                        "NOTEBOOK_SINGLE_IFRAME_PINNED": "latest",
                        "NOTEBOOK_STABLE_RUNTIME_ENVIRONMENT_VERSION": "warehouse:WH-RUNTIME-2.0",
                        "NOTEBOOK_UPDATE_PACKAGE_TIMEOUT_SECONDS": 300,
                        "OTHER_DOCS_BASE_URL": "https://other-docs.snowflake.com",
                        "PYTHON_ENABLE_ANACONDA_TERMS_ENFORCEMENT": true,
                        "SIGNUP_BASE_URL": "https://signup.snowflake.com",
                        "SMALL_WAREHOUSE_CLUSTER_COUNT_LIMIT": 300,
                        "SNOWSERVICES_DISABLE_TRIAL_ACCOUNT_ACCESS": true,
                        "STREAMLIT_WEBSOCKET_IDLE_TIMEOUT": 895000,
                        "TRUST_CENTER_PERSON_USER_MFA_ENFORCEMENT_READINESS_PERCENT": -1,
                        "TRUST_CENTER_SERVICE_USER_PASSWORDLESS_READINESS_PERCENT": -1,
                        "UI_DARK_MODE_ENABLE_TIME_EPOCH_SECONDS": **********,
                        "UI_DEFAULT_WORKSHEETS_TO_LOAD": "",
                        "UI_DEPRECATING_RESULT_SHARING_AND_SECONDARY_ROLES_IN_DASHBOARD": true,
                        "UI_ENABLE_AF_FEATURE_12": "enabled",
                        "UI_ENABLE_AF_FEATURE_13": "unset",
                        "UI_ENABLE_AF_FEATURE_15": "unset",
                        "UI_ENABLE_AF_FEATURE_18": "unset",
                        "UI_ENABLE_AF_FEATURE_21": "unset",
                        "UI_ENABLE_AF_FEATURE_23": "unset",
                        "UI_ENABLE_AF_FEATURE_24": "enabled",
                        "UI_ENABLE_AF_FEATURE_29": "unset",
                        "UI_ENABLE_AF_FEATURE_30": "unset",
                        "UI_ENABLE_AF_FEATURE_31": "unset",
                        "UI_ENABLE_AF_FEATURE_32": "enabled",
                        "UI_ENABLE_AF_FEATURE_33": "enabled",
                        "UI_ENABLE_AF_FEATURE_35": "unset",
                        "UI_ENABLE_AF_FEATURE_42": "unset",
                        "UI_ENABLE_AF_FEATURE_43": "unset",
                        "UI_ENABLE_AF_FEATURE_44": "unset",
                        "UI_ENABLE_AF_FEATURE_45": "unset",
                        "UI_ENABLE_AF_FEATURE_47": "unset",
                        "UI_ENABLE_AF_FEATURE_48": "unset",
                        "UI_ENABLE_AF_FEATURE_54": "unset",
                        "UI_ENABLE_AF_FEATURE_55": "unset",
                        "UI_ENABLE_AF_FEATURE_56": "unset",
                        "UI_ENABLE_AF_FEATURE_57": "unset",
                        "UI_ENABLE_AF_FEATURE_58": "unset",
                        "UI_ENABLE_AF_FEATURE_62": "ENABLED",
                        "UI_ENABLE_AF_FEATURE_63": "unset",
                        "UI_ENABLE_AF_FEATURE_64": "unset",
                        "UI_ENABLE_AF_FEATURE_65": "unset",
                        "UI_ENABLE_AF_FEATURE_66": "unset",
                        "UI_ENABLE_AF_FEATURE_67": "unset",
                        "UI_ENABLE_AF_FEATURE_68": "unset",
                        "UI_ENABLE_AF_FEATURE_69": "unset",
                        "UI_ENABLE_AF_FEATURE_70": "unset",
                        "UI_ENABLE_AF_FEATURE_71": "unset",
                        "UI_ENABLE_AF_FEATURE_72": "unset",
                        "UI_ENABLE_AF_FEATURE_73": "unset",
                        "UI_ENABLE_AF_FEATURE_74": "unset",
                        "UI_ENABLE_AF_FEATURE_75": "unset",
                        "UI_ENABLE_AF_FEATURE_76": "unset",
                        "UI_ENABLE_AF_FEATURE_77": "unset",
                        "UI_ENABLE_AF_FEATURE_78": "unset",
                        "UI_ENABLE_AF_FEATURE_79": "unset",
                        "UI_ENABLE_AF_FEATURE_80": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_1": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_10": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_100": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_11": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_12": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_13": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_14": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_15": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_16": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_17": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_18": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_19": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_2": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_20": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_21": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_22": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_23": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_24": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_25": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_26": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_27": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_28": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_29": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_3": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_30": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_31": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_32": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_33": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_34": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_35": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_36": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_37": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_38": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_39": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_4": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_40": "ENABLED",
                        "UI_ENABLE_AI_ML_FEATURE_41": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_42": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_43": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_44": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_45": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_46": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_47": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_48": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_49": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_5": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_50": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_51": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_52": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_53": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_54": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_55": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_56": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_57": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_58": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_59": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_6": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_60": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_61": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_62": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_63": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_64": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_65": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_66": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_67": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_68": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_69": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_7": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_70": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_71": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_72": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_73": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_74": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_75": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_76": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_77": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_78": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_79": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_8": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_80": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_81": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_82": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_83": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_84": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_85": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_86": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_87": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_88": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_89": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_9": "enabled",
                        "UI_ENABLE_AI_ML_FEATURE_90": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_91": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_92": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_93": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_94": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_95": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_96": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_97": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_98": "unset",
                        "UI_ENABLE_AI_ML_FEATURE_99": "unset",
                        "UI_ENABLE_APEX_FEATURE_0": "unset",
                        "UI_ENABLE_APEX_FEATURE_1": "unset",
                        "UI_ENABLE_APEX_FEATURE_100": "unset",
                        "UI_ENABLE_APEX_FEATURE_101": "unset",
                        "UI_ENABLE_APEX_FEATURE_102": "unset",
                        "UI_ENABLE_APEX_FEATURE_103": "unset",
                        "UI_ENABLE_APEX_FEATURE_104": "unset",
                        "UI_ENABLE_APEX_FEATURE_105": "unset",
                        "UI_ENABLE_APEX_FEATURE_106": "unset",
                        "UI_ENABLE_APEX_FEATURE_107": "unset",
                        "UI_ENABLE_APEX_FEATURE_108": "unset",
                        "UI_ENABLE_APEX_FEATURE_109": "unset",
                        "UI_ENABLE_APEX_FEATURE_110": "unset",
                        "UI_ENABLE_APEX_FEATURE_111": "unset",
                        "UI_ENABLE_APEX_FEATURE_112": "unset",
                        "UI_ENABLE_APEX_FEATURE_113": "unset",
                        "UI_ENABLE_APEX_FEATURE_114": "unset",
                        "UI_ENABLE_APEX_FEATURE_115": "unset",
                        "UI_ENABLE_APEX_FEATURE_116": "unset",
                        "UI_ENABLE_APEX_FEATURE_117": "unset",
                        "UI_ENABLE_APEX_FEATURE_118": "unset",
                        "UI_ENABLE_APEX_FEATURE_119": "unset",
                        "UI_ENABLE_APEX_FEATURE_12": "unset",
                        "UI_ENABLE_APEX_FEATURE_120": "unset",
                        "UI_ENABLE_APEX_FEATURE_121": "unset",
                        "UI_ENABLE_APEX_FEATURE_122": "unset",
                        "UI_ENABLE_APEX_FEATURE_123": "unset",
                        "UI_ENABLE_APEX_FEATURE_124": "unset",
                        "UI_ENABLE_APEX_FEATURE_125": "unset",
                        "UI_ENABLE_APEX_FEATURE_126": "unset",
                        "UI_ENABLE_APEX_FEATURE_127": "unset",
                        "UI_ENABLE_APEX_FEATURE_128": "unset",
                        "UI_ENABLE_APEX_FEATURE_129": "unset",
                        "UI_ENABLE_APEX_FEATURE_13": "unset",
                        "UI_ENABLE_APEX_FEATURE_130": "unset",
                        "UI_ENABLE_APEX_FEATURE_131": "unset",
                        "UI_ENABLE_APEX_FEATURE_132": "unset",
                        "UI_ENABLE_APEX_FEATURE_133": "unset",
                        "UI_ENABLE_APEX_FEATURE_134": "unset",
                        "UI_ENABLE_APEX_FEATURE_135": "unset",
                        "UI_ENABLE_APEX_FEATURE_136": "unset",
                        "UI_ENABLE_APEX_FEATURE_137": "unset",
                        "UI_ENABLE_APEX_FEATURE_138": "unset",
                        "UI_ENABLE_APEX_FEATURE_139": "unset",
                        "UI_ENABLE_APEX_FEATURE_14": "enabled",
                        "UI_ENABLE_APEX_FEATURE_140": "unset",
                        "UI_ENABLE_APEX_FEATURE_141": "unset",
                        "UI_ENABLE_APEX_FEATURE_142": "unset",
                        "UI_ENABLE_APEX_FEATURE_143": "unset",
                        "UI_ENABLE_APEX_FEATURE_144": "unset",
                        "UI_ENABLE_APEX_FEATURE_145": "unset",
                        "UI_ENABLE_APEX_FEATURE_146": "unset",
                        "UI_ENABLE_APEX_FEATURE_147": "unset",
                        "UI_ENABLE_APEX_FEATURE_148": "unset",
                        "UI_ENABLE_APEX_FEATURE_149": "unset",
                        "UI_ENABLE_APEX_FEATURE_15": "unset",
                        "UI_ENABLE_APEX_FEATURE_150": "unset",
                        "UI_ENABLE_APEX_FEATURE_151": "unset",
                        "UI_ENABLE_APEX_FEATURE_152": "unset",
                        "UI_ENABLE_APEX_FEATURE_153": "unset",
                        "UI_ENABLE_APEX_FEATURE_154": "unset",
                        "UI_ENABLE_APEX_FEATURE_155": "unset",
                        "UI_ENABLE_APEX_FEATURE_156": "unset",
                        "UI_ENABLE_APEX_FEATURE_157": "unset",
                        "UI_ENABLE_APEX_FEATURE_158": "unset",
                        "UI_ENABLE_APEX_FEATURE_159": "unset",
                        "UI_ENABLE_APEX_FEATURE_16": "unset",
                        "UI_ENABLE_APEX_FEATURE_160": "unset",
                        "UI_ENABLE_APEX_FEATURE_17": "unset",
                        "UI_ENABLE_APEX_FEATURE_18": "unset",
                        "UI_ENABLE_APEX_FEATURE_19": "unset",
                        "UI_ENABLE_APEX_FEATURE_2": "unset",
                        "UI_ENABLE_APEX_FEATURE_20": "unset",
                        "UI_ENABLE_APEX_FEATURE_21": "unset",
                        "UI_ENABLE_APEX_FEATURE_22": "unset",
                        "UI_ENABLE_APEX_FEATURE_23": "disabled",
                        "UI_ENABLE_APEX_FEATURE_24": "unset",
                        "UI_ENABLE_APEX_FEATURE_25": "unset",
                        "UI_ENABLE_APEX_FEATURE_26": "unset",
                        "UI_ENABLE_APEX_FEATURE_27": "enabled",
                        "UI_ENABLE_APEX_FEATURE_28": "unset",
                        "UI_ENABLE_APEX_FEATURE_29": "enabled",
                        "UI_ENABLE_APEX_FEATURE_30": "unset",
                        "UI_ENABLE_APEX_FEATURE_31": "unset",
                        "UI_ENABLE_APEX_FEATURE_33": "unset",
                        "UI_ENABLE_APEX_FEATURE_34": "unset",
                        "UI_ENABLE_APEX_FEATURE_35": "unset",
                        "UI_ENABLE_APEX_FEATURE_36": "unset",
                        "UI_ENABLE_APEX_FEATURE_37": "unset",
                        "UI_ENABLE_APEX_FEATURE_38": "unset",
                        "UI_ENABLE_APEX_FEATURE_39": "unset",
                        "UI_ENABLE_APEX_FEATURE_40": "unset",
                        "UI_ENABLE_APEX_FEATURE_41": "unset",
                        "UI_ENABLE_APEX_FEATURE_42": "unset",
                        "UI_ENABLE_APEX_FEATURE_44": "disabled",
                        "UI_ENABLE_APEX_FEATURE_45": "disabled",
                        "UI_ENABLE_APEX_FEATURE_46": "unset",
                        "UI_ENABLE_APEX_FEATURE_47": "enabled",
                        "UI_ENABLE_APEX_FEATURE_48": "unset",
                        "UI_ENABLE_APEX_FEATURE_49": "unset",
                        "UI_ENABLE_APEX_FEATURE_5": "unset",
                        "UI_ENABLE_APEX_FEATURE_50": "unset",
                        "UI_ENABLE_APEX_FEATURE_51": "enabled",
                        "UI_ENABLE_APEX_FEATURE_52": "unset",
                        "UI_ENABLE_APEX_FEATURE_53": "unset",
                        "UI_ENABLE_APEX_FEATURE_54": "unset",
                        "UI_ENABLE_APEX_FEATURE_56": "enabled",
                        "UI_ENABLE_APEX_FEATURE_58": "unset",
                        "UI_ENABLE_APEX_FEATURE_59": "enabled",
                        "UI_ENABLE_APEX_FEATURE_60": "enabled",
                        "UI_ENABLE_APEX_FEATURE_61": "unset",
                        "UI_ENABLE_APEX_FEATURE_62": "unset",
                        "UI_ENABLE_APEX_FEATURE_63": "unset",
                        "UI_ENABLE_APEX_FEATURE_64": "unset",
                        "UI_ENABLE_APEX_FEATURE_66": "unset",
                        "UI_ENABLE_APEX_FEATURE_67": "unset",
                        "UI_ENABLE_APEX_FEATURE_69": "unset",
                        "UI_ENABLE_APEX_FEATURE_7": "enabled",
                        "UI_ENABLE_APEX_FEATURE_70": "enabled",
                        "UI_ENABLE_APEX_FEATURE_71": "unset",
                        "UI_ENABLE_APEX_FEATURE_72": "enabled",
                        "UI_ENABLE_APEX_FEATURE_73": "unset",
                        "UI_ENABLE_APEX_FEATURE_74": "unset",
                        "UI_ENABLE_APEX_FEATURE_75": "unset",
                        "UI_ENABLE_APEX_FEATURE_76": "unset",
                        "UI_ENABLE_APEX_FEATURE_77": "enabled",
                        "UI_ENABLE_APEX_FEATURE_78": "enabled",
                        "UI_ENABLE_APEX_FEATURE_79": "enabled",
                        "UI_ENABLE_APEX_FEATURE_8": "unset",
                        "UI_ENABLE_APEX_FEATURE_81": "unset",
                        "UI_ENABLE_APEX_FEATURE_82": "unset",
                        "UI_ENABLE_APEX_FEATURE_83": "unset",
                        "UI_ENABLE_APEX_FEATURE_84": "enabled",
                        "UI_ENABLE_APEX_FEATURE_85": "enabled",
                        "UI_ENABLE_APEX_FEATURE_87": "unset",
                        "UI_ENABLE_APEX_FEATURE_88": "unset",
                        "UI_ENABLE_APEX_FEATURE_89": "unset",
                        "UI_ENABLE_APEX_FEATURE_9": "unset",
                        "UI_ENABLE_APEX_FEATURE_90": "unset",
                        "UI_ENABLE_APEX_FEATURE_91": "ENABLED",
                        "UI_ENABLE_APEX_FEATURE_92": "unset",
                        "UI_ENABLE_APEX_FEATURE_93": "enabled",
                        "UI_ENABLE_APEX_FEATURE_94": "unset",
                        "UI_ENABLE_APEX_FEATURE_95": "unset",
                        "UI_ENABLE_APEX_FEATURE_96": "unset",
                        "UI_ENABLE_APEX_FEATURE_97": "unset",
                        "UI_ENABLE_APEX_FEATURE_98": "unset",
                        "UI_ENABLE_APEX_FEATURE_99": "unset",
                        "UI_ENABLE_APM_METRICS_IN_PACKAGE": "enabled",
                        "UI_ENABLE_APPS_ASYNC_INSTALL": "enabled",
                        "UI_ENABLE_AUTO_REFRESH_IN_CREATE_EDIT_STAGE_DIALOG": "unset",
                        "UI_ENABLE_AUTO_SHARE_DATA_LISTINGS_WITH_MPOPS": "unset",
                        "UI_ENABLE_AUTO_SHARE_DRAFTS_WITH_MPOPS": "enabled",
                        "UI_ENABLE_AX_ADAPTIVE_WAREHOUSE": "DISABLED",
                        "UI_ENABLE_AX_AUTO_OBJECT_DESCRIPTION": "ENABLED",
                        "UI_ENABLE_AX_FEATURE_0": "enabled",
                        "UI_ENABLE_AX_FEATURE_1": "unset",
                        "UI_ENABLE_AX_FEATURE_17": "enabled",
                        "UI_ENABLE_AX_FEATURE_18": "unset",
                        "UI_ENABLE_AX_FEATURE_24": "enabled",
                        "UI_ENABLE_AX_FEATURE_25": "enabled",
                        "UI_ENABLE_AX_FEATURE_27": "enabled",
                        "UI_ENABLE_AX_FEATURE_28": "enabled",
                        "UI_ENABLE_AX_FEATURE_30": "DISABLED",
                        "UI_ENABLE_AX_FEATURE_31": "enabled",
                        "UI_ENABLE_AX_FEATURE_33": "enabled",
                        "UI_ENABLE_AX_FEATURE_34": "enabled",
                        "UI_ENABLE_AX_FEATURE_35": "unset",
                        "UI_ENABLE_AX_FEATURE_8": "ENABLED",
                        "UI_ENABLE_AX_FEATURE_9": "unset",
                        "UI_ENABLE_AX_MULTI_FETCH_TABS_OBJECT_EXPLORER": true,
                        "UI_ENABLE_AX_OBJECT_INSIGHTS": "unset",
                        "UI_ENABLE_AX_TAG_PROPAGATION": "ENABLED",
                        "UI_ENABLE_BILLING_AND_TERMS_REFACTOR": "ENABLED",
                        "UI_ENABLE_BILLING_CONTACTS": false,
                        "UI_ENABLE_BILLING_DOCUMENTS": true,
                        "UI_ENABLE_BUY_MONETIZED_LISTING": true,
                        "UI_ENABLE_CASE_CATEGORIES_ENDPOINT": "enabled",
                        "UI_ENABLE_CHART_FORECASTING": "unset",
                        "UI_ENABLE_CHROMELESS_SIS_APPS": "unset",
                        "UI_ENABLE_CM6_SQL_FORCED_BOUNDARIES": "unset",
                        "UI_ENABLE_COLUMN_CONFIGURATION": "unset",
                        "UI_ENABLE_CONSUMER_FEATURE_TRIAL_DETAILS_IN_LISTING_API": "enabled",
                        "UI_ENABLE_CONSUMPTION_PAGE": "enabled",
                        "UI_ENABLE_COPILOT": "unset",
                        "UI_ENABLE_COPILOT_CHAT_HISTORY": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_01": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_02": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_03": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_04": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_05": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_06": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_07": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_08": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_09": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_10": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_11": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_12": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_13": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_14": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_15": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_16": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_17": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_18": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_19": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_20": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_21": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_22": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_23": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_24": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_25": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_26": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_27": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_28": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_29": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_30": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_31": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_32": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_33": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_34": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_35": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_36": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_37": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_38": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_39": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_40": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_41": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_42": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_43": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_44": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_45": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_46": "enabled",
                        "UI_ENABLE_COPILOT_FEATURE_47": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_48": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_49": "unset",
                        "UI_ENABLE_COPILOT_FEATURE_50": "unset",
                        "UI_ENABLE_COPILOT_INFERENCE": "unset",
                        "UI_ENABLE_COPILOT_INFERENCE_GPT4_ALLOWED": "unset",
                        "UI_ENABLE_COPILOT_ML_TELEMETRY": "unset",
                        "UI_ENABLE_COST_ANOMALY_DETECTION": "ENABLED_PUBLIC_PREVIEW",
                        "UI_ENABLE_CUSTOMER_CONTENT_SUGGESTION": "enabled",
                        "UI_ENABLE_DASHBOARD_5_FEATURE": false,
                        "UI_ENABLE_DATA_LOADING_WIZARD": false,
                        "UI_ENABLE_DATA_PREVIEW_ADMIN": "enabled",
                        "UI_ENABLE_DATA_PREVIEW_CONSUMER": "enabled",
                        "UI_ENABLE_DATA_PREVIEW_M2_PROVIDER": "enabled",
                        "UI_ENABLE_DATA_PREVIEW_PROVIDER": "enabled",
                        "UI_ENABLE_DCR_ASSIGN_POLICIES_TO_TABLE": "unset",
                        "UI_ENABLE_DEFAULT_WAREHOUSE_NUDGE": "ENABLED",
                        "UI_ENABLE_DEX_FEATURE_10": "unset",
                        "UI_ENABLE_DEX_FEATURE_15": "enabled",
                        "UI_ENABLE_DEX_FEATURE_19": "enabled",
                        "UI_ENABLE_DEX_FEATURE_20": "unset",
                        "UI_ENABLE_DEX_FEATURE_21": "unset",
                        "UI_ENABLE_DEX_FEATURE_22": "unset",
                        "UI_ENABLE_DEX_FEATURE_23": "unset",
                        "UI_ENABLE_DEX_FEATURE_26": "unset",
                        "UI_ENABLE_DEX_FEATURE_9": "unset",
                        "UI_ENABLE_DG_LINEAGE": "enabled",
                        "UI_ENABLE_DIRECTORY_TABLES_FOR_INTERNAL_STAGES": "unset",
                        "UI_ENABLE_DMX_FEATURE_100": "unset",
                        "UI_ENABLE_DMX_FEATURE_101": "enabled",
                        "UI_ENABLE_DMX_FEATURE_104": "unset",
                        "UI_ENABLE_DMX_FEATURE_106": "enabled",
                        "UI_ENABLE_DMX_FEATURE_113": "unset",
                        "UI_ENABLE_DMX_FEATURE_115": "unset",
                        "UI_ENABLE_DMX_FEATURE_116": "unset",
                        "UI_ENABLE_DMX_FEATURE_117": "enabled",
                        "UI_ENABLE_DMX_FEATURE_118": "unset",
                        "UI_ENABLE_DMX_FEATURE_120": "unset",
                        "UI_ENABLE_DMX_FEATURE_121": "enabled",
                        "UI_ENABLE_DMX_FEATURE_125": "enabled",
                        "UI_ENABLE_DMX_FEATURE_126": "enabled",
                        "UI_ENABLE_DMX_FEATURE_127": "enabled",
                        "UI_ENABLE_DMX_FEATURE_129": "unset",
                        "UI_ENABLE_DMX_FEATURE_130": "enabled",
                        "UI_ENABLE_DMX_FEATURE_132": "unset",
                        "UI_ENABLE_DMX_FEATURE_133": "unset",
                        "UI_ENABLE_DMX_FEATURE_134": "unset",
                        "UI_ENABLE_DMX_FEATURE_135": "unset",
                        "UI_ENABLE_DMX_FEATURE_136": "unset",
                        "UI_ENABLE_DMX_FEATURE_137": "unset",
                        "UI_ENABLE_DMX_FEATURE_138": "enabled",
                        "UI_ENABLE_DMX_FEATURE_139": "unset",
                        "UI_ENABLE_DMX_FEATURE_140": "unset",
                        "UI_ENABLE_DMX_FEATURE_142": "unset",
                        "UI_ENABLE_DMX_FEATURE_143": "unset",
                        "UI_ENABLE_DMX_FEATURE_144": "unset",
                        "UI_ENABLE_DMX_FEATURE_145": "unset",
                        "UI_ENABLE_DMX_FEATURE_148": "unset",
                        "UI_ENABLE_DMX_FEATURE_149": "ENABLED",
                        "UI_ENABLE_DMX_FEATURE_150": "enabled",
                        "UI_ENABLE_DMX_FEATURE_151": "unset",
                        "UI_ENABLE_DMX_FEATURE_153": "unset",
                        "UI_ENABLE_DMX_FEATURE_154": "unset",
                        "UI_ENABLE_DMX_FEATURE_155": "unset",
                        "UI_ENABLE_DMX_FEATURE_156": "enabled",
                        "UI_ENABLE_DMX_FEATURE_157": "unset",
                        "UI_ENABLE_DMX_FEATURE_158": "unset",
                        "UI_ENABLE_DMX_FEATURE_160": "enabled",
                        "UI_ENABLE_DMX_FEATURE_161": "unset",
                        "UI_ENABLE_DMX_FEATURE_163": "unset",
                        "UI_ENABLE_DMX_FEATURE_164": "unset",
                        "UI_ENABLE_DMX_FEATURE_165": "unset",
                        "UI_ENABLE_DMX_FEATURE_166": "unset",
                        "UI_ENABLE_DMX_FEATURE_167": "unset",
                        "UI_ENABLE_DMX_FEATURE_168": "unset",
                        "UI_ENABLE_DMX_FEATURE_169": "unset",
                        "UI_ENABLE_DMX_FEATURE_170": "enabled",
                        "UI_ENABLE_DMX_FEATURE_171": "enabled",
                        "UI_ENABLE_DMX_FEATURE_172": "enabled",
                        "UI_ENABLE_DMX_FEATURE_173": "enabled",
                        "UI_ENABLE_DMX_FEATURE_174": "enabled",
                        "UI_ENABLE_DMX_FEATURE_175": "enabled",
                        "UI_ENABLE_DMX_FEATURE_176": "enabled",
                        "UI_ENABLE_DMX_FEATURE_177": "unset",
                        "UI_ENABLE_DMX_FEATURE_178": "unset",
                        "UI_ENABLE_DMX_FEATURE_179": "enabled",
                        "UI_ENABLE_DMX_FEATURE_180": "unset",
                        "UI_ENABLE_DMX_FEATURE_182": "unset",
                        "UI_ENABLE_DMX_FEATURE_183": "enabled",
                        "UI_ENABLE_DMX_FEATURE_184": "disabled",
                        "UI_ENABLE_DMX_FEATURE_185": "unset",
                        "UI_ENABLE_DMX_FEATURE_186": "unset",
                        "UI_ENABLE_DMX_FEATURE_187": "unset",
                        "UI_ENABLE_DMX_FEATURE_188": "unset",
                        "UI_ENABLE_DMX_FEATURE_189": "unset",
                        "UI_ENABLE_DMX_FEATURE_190": "ENABLED",
                        "UI_ENABLE_DMX_FEATURE_191": "enabled",
                        "UI_ENABLE_DMX_FEATURE_192": "unset",
                        "UI_ENABLE_DMX_FEATURE_193": "unset",
                        "UI_ENABLE_DMX_FEATURE_195": "enabled",
                        "UI_ENABLE_DMX_FEATURE_196": "unset",
                        "UI_ENABLE_DMX_FEATURE_197": "unset",
                        "UI_ENABLE_DMX_FEATURE_198": "unset",
                        "UI_ENABLE_DMX_FEATURE_199": "enabled",
                        "UI_ENABLE_DMX_FEATURE_200": "unset",
                        "UI_ENABLE_DMX_FEATURE_201": "enabled",
                        "UI_ENABLE_DMX_FEATURE_202": "enabled",
                        "UI_ENABLE_DMX_FEATURE_205": "unset",
                        "UI_ENABLE_DMX_FEATURE_206": "enabled",
                        "UI_ENABLE_DMX_FEATURE_207": "unset",
                        "UI_ENABLE_DMX_FEATURE_208": "unset",
                        "UI_ENABLE_DMX_FEATURE_209": "unset",
                        "UI_ENABLE_DMX_FEATURE_21": "unset",
                        "UI_ENABLE_DMX_FEATURE_210": "unset",
                        "UI_ENABLE_DMX_FEATURE_211": "unset",
                        "UI_ENABLE_DMX_FEATURE_212": "unset",
                        "UI_ENABLE_DMX_FEATURE_213": "unset",
                        "UI_ENABLE_DMX_FEATURE_214": "unset",
                        "UI_ENABLE_DMX_FEATURE_215": "unset",
                        "UI_ENABLE_DMX_FEATURE_216": "unset",
                        "UI_ENABLE_DMX_FEATURE_217": "unset",
                        "UI_ENABLE_DMX_FEATURE_218": "unset",
                        "UI_ENABLE_DMX_FEATURE_219": "unset",
                        "UI_ENABLE_DMX_FEATURE_22": "enabled",
                        "UI_ENABLE_DMX_FEATURE_220": "unset",
                        "UI_ENABLE_DMX_FEATURE_221": "unset",
                        "UI_ENABLE_DMX_FEATURE_222": "unset",
                        "UI_ENABLE_DMX_FEATURE_223": "enabled",
                        "UI_ENABLE_DMX_FEATURE_224": "unset",
                        "UI_ENABLE_DMX_FEATURE_225": "unset",
                        "UI_ENABLE_DMX_FEATURE_226": "unset",
                        "UI_ENABLE_DMX_FEATURE_227": "unset",
                        "UI_ENABLE_DMX_FEATURE_229": "unset",
                        "UI_ENABLE_DMX_FEATURE_23": "enabled",
                        "UI_ENABLE_DMX_FEATURE_232": "unset",
                        "UI_ENABLE_DMX_FEATURE_233": "unset",
                        "UI_ENABLE_DMX_FEATURE_234": "unset",
                        "UI_ENABLE_DMX_FEATURE_235": "unset",
                        "UI_ENABLE_DMX_FEATURE_236": "unset",
                        "UI_ENABLE_DMX_FEATURE_237": "unset",
                        "UI_ENABLE_DMX_FEATURE_238": "enabled",
                        "UI_ENABLE_DMX_FEATURE_239": "unset",
                        "UI_ENABLE_DMX_FEATURE_240": "unset",
                        "UI_ENABLE_DMX_FEATURE_241": "unset",
                        "UI_ENABLE_DMX_FEATURE_242": "unset",
                        "UI_ENABLE_DMX_FEATURE_243": "unset",
                        "UI_ENABLE_DMX_FEATURE_244": "unset",
                        "UI_ENABLE_DMX_FEATURE_245": "unset",
                        "UI_ENABLE_DMX_FEATURE_248": "unset",
                        "UI_ENABLE_DMX_FEATURE_249": "unset",
                        "UI_ENABLE_DMX_FEATURE_250": "enabled",
                        "UI_ENABLE_DMX_FEATURE_251": "unset",
                        "UI_ENABLE_DMX_FEATURE_252": "unset",
                        "UI_ENABLE_DMX_FEATURE_253": "unset",
                        "UI_ENABLE_DMX_FEATURE_254": "unset",
                        "UI_ENABLE_DMX_FEATURE_255": "unset",
                        "UI_ENABLE_DMX_FEATURE_256": "unset",
                        "UI_ENABLE_DMX_FEATURE_257": "unset",
                        "UI_ENABLE_DMX_FEATURE_259": "unset",
                        "UI_ENABLE_DMX_FEATURE_260": "unset",
                        "UI_ENABLE_DMX_FEATURE_261": "unset",
                        "UI_ENABLE_DMX_FEATURE_262": "unset",
                        "UI_ENABLE_DMX_FEATURE_263": "unset",
                        "UI_ENABLE_DMX_FEATURE_264": "unset",
                        "UI_ENABLE_DMX_FEATURE_266": "unset",
                        "UI_ENABLE_DMX_FEATURE_267": "unset",
                        "UI_ENABLE_DMX_FEATURE_268": "unset",
                        "UI_ENABLE_DMX_FEATURE_269": "unset",
                        "UI_ENABLE_DMX_FEATURE_270": "enabled",
                        "UI_ENABLE_DMX_FEATURE_271": "unset",
                        "UI_ENABLE_DMX_FEATURE_272": "unset",
                        "UI_ENABLE_DMX_FEATURE_4": "unset",
                        "UI_ENABLE_DMX_FEATURE_47": "unset",
                        "UI_ENABLE_DMX_FEATURE_49": "enabled",
                        "UI_ENABLE_DMX_FEATURE_54": "unset",
                        "UI_ENABLE_DMX_FEATURE_58": "unset",
                        "UI_ENABLE_DMX_FEATURE_62": "unset",
                        "UI_ENABLE_DMX_FEATURE_68": "unset",
                        "UI_ENABLE_DMX_FEATURE_69": "unset",
                        "UI_ENABLE_DMX_FEATURE_73": "unset",
                        "UI_ENABLE_DMX_FEATURE_75": "unset",
                        "UI_ENABLE_DMX_FEATURE_76": "enabled",
                        "UI_ENABLE_DMX_FEATURE_77": "enabled",
                        "UI_ENABLE_DMX_FEATURE_85": "unset",
                        "UI_ENABLE_DMX_FEATURE_88": "enabled",
                        "UI_ENABLE_DMX_FEATURE_89": "unset",
                        "UI_ENABLE_DMX_FEATURE_93": "unset",
                        "UI_ENABLE_DMX_FEATURE_94": "unset",
                        "UI_ENABLE_DMX_FEATURE_95": "disabled",
                        "UI_ENABLE_DMX_FEATURE_96": "unset",
                        "UI_ENABLE_DMX_FEATURE_97": "unset",
                        "UI_ENABLE_DMX_SNOWSCOPE_SEARCH": "enabled",
                        "UI_ENABLE_DNA_LOOSE_OBJECT_SEARCH": "unset",
                        "UI_ENABLE_DOCUMENT_AI": "enabled",
                        "UI_ENABLE_DOCUMENT_AI_HIDE_PREVIEW_BADGE": "enabled",
                        "UI_ENABLE_DOCUMENT_AI_HIGHLIGHT_ANSWERS": "enabled",
                        "UI_ENABLE_DOCUMENT_AI_MODEL_REGISTRY": "disabled",
                        "UI_ENABLE_DOCUMENT_AI_MODEL_REGISTRY_MIGRATION": "disabled",
                        "UI_ENABLE_DOCUMENT_AI_PLAYGROUND": "disabled",
                        "UI_ENABLE_DOCUMENT_AI_PRPR": "unset",
                        "UI_ENABLE_DOCUMENT_AI_SKIP_FETCH_WITHOUT_ROLE": "enabled",
                        "UI_ENABLE_DOCUMENT_AI_TABLE_EXTRACTION": "enabled",
                        "UI_ENABLE_DOCUMENT_AI_VGE": "unset",
                        "UI_ENABLE_DOCUMENT_INTELLIGENCE_PRPR": "unset",
                        "UI_ENABLE_DYNAMIC_OPEN_GRAPH_IMAGES": "unset",
                        "UI_ENABLE_EGRESS_COST_OPTIMIZER": "enabled",
                        "UI_ENABLE_ENHANCED_EDIT_TASK": "unset",
                        "UI_ENABLE_EXTERNAL_STAGES_CREATE_EDIT": "unset",
                        "UI_ENABLE_FEATURED_MARKETPLACE_PAGE_ENDPOINT": "unset",
                        "UI_ENABLE_FEATURE_ACCOUNT_REPLICATION": "enabled",
                        "UI_ENABLE_FEATURE_ANACONDA_ENABLEMENT": true,
                        "UI_ENABLE_FEATURE_AX_PUPR": false,
                        "UI_ENABLE_FEATURE_CG_BUDGET": "enabled",
                        "UI_ENABLE_FEATURE_CG_BUDGET_VERSION": 3,
                        "UI_ENABLE_FEATURE_CG_TAGGING": "enabled",
                        "UI_ENABLE_FEATURE_CLIENT_REDIRECTS": "enabled",
                        "UI_ENABLE_FEATURE_CONSUMER_PCP_GOOD_BETTER_BEST_PRICING_PLANS": "enabled",
                        "UI_ENABLE_FEATURE_CONSUMER_SEPARATE_PRICING_PLAN_AND_PUBLIC_OFFER_CREATION": "unset",
                        "UI_ENABLE_FEATURE_CONTACT_SALES_PRICING_PLAN_ALLOW_MULTIPLE_EMAILS": "unset",
                        "UI_ENABLE_FEATURE_CROSS_ROLE_SHARING": false,
                        "UI_ENABLE_FEATURE_DX_HEALTH_CHECK": "unset",
                        "UI_ENABLE_FEATURE_LISTING_PRICING_PLAN_REFACTORS": "enabled",
                        "UI_ENABLE_FEATURE_MFA_SELF_SERVICE": true,
                        "UI_ENABLE_FEATURE_PROVIDER_CREDIT_SURCHARGE": "unset",
                        "UI_ENABLE_FEATURE_PROVIDER_PCP_GOOD_BETTER_BEST_PRICING_PLANS": "enabled",
                        "UI_ENABLE_FEATURE_PROVIDER_SEPARATE_PRICING_PLAN_AND_PUBLIC_OFFER_CREATION": "unset",
                        "UI_ENABLE_FEATURE_QUERY_ACCELERATION_SERVICE": true,
                        "UI_ENABLE_FEATURE_SETUP_SCRIPTS_VALIDATION": "enabled",
                        "UI_ENABLE_FEATURE_TRIAL_DETAILS_IN_LISTING_API": "enabled",
                        "UI_ENABLE_FEATURE_TRUST_CENTER_EVIDENCE_COLLECTION": "ENABLED",
                        "UI_ENABLE_FEATURE_TRUST_CENTER_NOTIFICATION": "ENABLED",
                        "UI_ENABLE_FEATURE_TRUST_CENTER_TOP_LEVEL_METRICS": "ENABLED",
                        "UI_ENABLE_FEATURE_UNISTORE": "ENABLED",
                        "UI_ENABLE_FEATURE_USAGE_PAGE": true,
                        "UI_ENABLE_FULL_PAGE_CASE_FORM": "unset",
                        "UI_ENABLE_GLOBAL_NAV": false,
                        "UI_ENABLE_GROWTH_FEATURE_1": true,
                        "UI_ENABLE_GROWTH_FEATURE_10": true,
                        "UI_ENABLE_GROWTH_FEATURE_11": true,
                        "UI_ENABLE_GROWTH_FEATURE_15": true,
                        "UI_ENABLE_GROWTH_FEATURE_2": false,
                        "UI_ENABLE_GROWTH_FEATURE_21": false,
                        "UI_ENABLE_GROWTH_FEATURE_22": true,
                        "UI_ENABLE_GROWTH_FEATURE_23": true,
                        "UI_ENABLE_GROWTH_FEATURE_27": false,
                        "UI_ENABLE_GROWTH_FEATURE_3": false,
                        "UI_ENABLE_GROWTH_FEATURE_30": true,
                        "UI_ENABLE_GROWTH_FEATURE_31": false,
                        "UI_ENABLE_GROWTH_FEATURE_32": false,
                        "UI_ENABLE_GROWTH_FEATURE_33": false,
                        "UI_ENABLE_GROWTH_FEATURE_34": false,
                        "UI_ENABLE_GROWTH_FEATURE_35": false,
                        "UI_ENABLE_GROWTH_FEATURE_36": false,
                        "UI_ENABLE_GROWTH_FEATURE_37": false,
                        "UI_ENABLE_GROWTH_FEATURE_38": false,
                        "UI_ENABLE_GROWTH_FEATURE_39": false,
                        "UI_ENABLE_GROWTH_FEATURE_40": false,
                        "UI_ENABLE_GROWTH_FEATURE_41": false,
                        "UI_ENABLE_GROWTH_FEATURE_42": false,
                        "UI_ENABLE_GROWTH_FEATURE_43": false,
                        "UI_ENABLE_GROWTH_FEATURE_44": false,
                        "UI_ENABLE_GROWTH_FEATURE_45": false,
                        "UI_ENABLE_GROWTH_FEATURE_46": false,
                        "UI_ENABLE_GROWTH_FEATURE_47": false,
                        "UI_ENABLE_GROWTH_FEATURE_48": false,
                        "UI_ENABLE_GROWTH_FEATURE_49": false,
                        "UI_ENABLE_GROWTH_FEATURE_5": true,
                        "UI_ENABLE_GROWTH_FEATURE_50": false,
                        "UI_ENABLE_GROWTH_FEATURE_51": false,
                        "UI_ENABLE_GROWTH_FEATURE_52": false,
                        "UI_ENABLE_GROWTH_FEATURE_53": false,
                        "UI_ENABLE_GROWTH_FEATURE_54": false,
                        "UI_ENABLE_GROWTH_FEATURE_55": false,
                        "UI_ENABLE_GROWTH_FEATURE_56": false,
                        "UI_ENABLE_GROWTH_FEATURE_57": false,
                        "UI_ENABLE_GROWTH_FEATURE_58": false,
                        "UI_ENABLE_GROWTH_FEATURE_59": false,
                        "UI_ENABLE_GROWTH_FEATURE_60": false,
                        "UI_ENABLE_GROWTH_FEATURE_61": false,
                        "UI_ENABLE_GROWTH_FEATURE_62": false,
                        "UI_ENABLE_GROWTH_FEATURE_63": false,
                        "UI_ENABLE_GROWTH_FEATURE_64": false,
                        "UI_ENABLE_GROWTH_FEATURE_65": false,
                        "UI_ENABLE_GROWTH_FEATURE_66": false,
                        "UI_ENABLE_GROWTH_FEATURE_67": false,
                        "UI_ENABLE_GROWTH_FEATURE_68": false,
                        "UI_ENABLE_GROWTH_FEATURE_69": false,
                        "UI_ENABLE_GROWTH_FEATURE_7": true,
                        "UI_ENABLE_GROWTH_FEATURE_70": false,
                        "UI_ENABLE_GROWTH_FEATURE_71": false,
                        "UI_ENABLE_GROWTH_FEATURE_72": false,
                        "UI_ENABLE_GROWTH_FEATURE_73": false,
                        "UI_ENABLE_GROWTH_FEATURE_74": false,
                        "UI_ENABLE_GROWTH_FEATURE_75": false,
                        "UI_ENABLE_GROWTH_FEATURE_76": false,
                        "UI_ENABLE_GROWTH_FEATURE_77": false,
                        "UI_ENABLE_GROWTH_FEATURE_78": false,
                        "UI_ENABLE_GROWTH_FEATURE_79": false,
                        "UI_ENABLE_GROWTH_FEATURE_80": false,
                        "UI_ENABLE_GROWTH_FEATURE_81": false,
                        "UI_ENABLE_GROWTH_FEATURE_82": false,
                        "UI_ENABLE_GROWTH_FEATURE_83": false,
                        "UI_ENABLE_GROWTH_FEATURE_84": false,
                        "UI_ENABLE_GROWTH_FEATURE_85": false,
                        "UI_ENABLE_GROWTH_FEATURE_86": false,
                        "UI_ENABLE_GROWTH_FEATURE_87": false,
                        "UI_ENABLE_GROWTH_FEATURE_88": false,
                        "UI_ENABLE_GROWTH_FEATURE_89": false,
                        "UI_ENABLE_GROWTH_FEATURE_90": false,
                        "UI_ENABLE_GUEST_FREE_TO_TRY_SECTION": "unset",
                        "UI_ENABLE_GUEST_SNOWSCOPE_SEARCH_MOBILE_CANARY": "unset",
                        "UI_ENABLE_GUEST_SNOWSCOPE_SEARCH_MOBILE_PROD": "enabled",
                        "UI_ENABLE_GUIDES_ON_LEARN_PAGE": false,
                        "UI_ENABLE_HOMEPAGE": true,
                        "UI_ENABLE_IMPORTED_DB_LIMIT_SECTION": "unset",
                        "UI_ENABLE_INTERNAL_LISTING_DETAIL_PAGE": "enabled",
                        "UI_ENABLE_INTERNAL_LISTING_OE_TREE": "enabled",
                        "UI_ENABLE_INTERNAL_MARKETPLACE_DYNAMIC_HEADER": "unset",
                        "UI_ENABLE_IN_APP_CONTACT_EMAIL_NUDGE": "ENABLED",
                        "UI_ENABLE_IN_APP_NOTIFICATIONS": "ENABLED",
                        "UI_ENABLE_IN_PRODUCT_SEARCH": false,
                        "UI_ENABLE_JS_WORKSHEETS": "unset",
                        "UI_ENABLE_JVM_WORKSHEETS": "unset",
                        "UI_ENABLE_LEARN": false,
                        "UI_ENABLE_LINEAGE_NEXT_LEVEL_FETCH": "enabled",
                        "UI_ENABLE_LISTING_DETAIL_PAGE_REDESIGN": "unset",
                        "UI_ENABLE_LOAD_FILE_DATA_PREVIEW": "Enabled",
                        "UI_ENABLE_LOG_EXPLORER": "enabled",
                        "UI_ENABLE_MARKETPLACE_INVOICER_UI": "ENABLED",
                        "UI_ENABLE_MLPF": "unset",
                        "UI_ENABLE_MLPF_FEATURE_01": "enabled",
                        "UI_ENABLE_MLPF_FEATURE_02": "unset",
                        "UI_ENABLE_MLPF_FEATURE_03": "unset",
                        "UI_ENABLE_MLPF_FEATURE_04": "enabled",
                        "UI_ENABLE_MLPF_FEATURE_05": "unset",
                        "UI_ENABLE_MLPF_FEATURE_06": "enabled",
                        "UI_ENABLE_MLPF_FEATURE_07": "ENABLED",
                        "UI_ENABLE_MLPF_FEATURE_08": "unset",
                        "UI_ENABLE_MLPF_FEATURE_09": "enabled",
                        "UI_ENABLE_MLPF_FEATURE_10": "ENABLED",
                        "UI_ENABLE_MLPF_FEATURE_11": "unset",
                        "UI_ENABLE_MLPF_FEATURE_12": "unset",
                        "UI_ENABLE_MLPF_FEATURE_13": "enabled",
                        "UI_ENABLE_MLPF_FEATURE_14": "enabled",
                        "UI_ENABLE_MLPF_FEATURE_15": "enabled",
                        "UI_ENABLE_MLPF_FEATURE_16": "unset",
                        "UI_ENABLE_MLPF_FEATURE_17": "unset",
                        "UI_ENABLE_MLPF_FEATURE_18": "ENABLED",
                        "UI_ENABLE_MLPF_FEATURE_19": "unset",
                        "UI_ENABLE_MLPF_FEATURE_20": "unset",
                        "UI_ENABLE_MLPF_FEATURE_21": "unset",
                        "UI_ENABLE_MLPF_FEATURE_22": "unset",
                        "UI_ENABLE_MLPF_FEATURE_23": "unset",
                        "UI_ENABLE_MLPF_FEATURE_24": "unset",
                        "UI_ENABLE_MLPF_FEATURE_25": "unset",
                        "UI_ENABLE_MLPF_FEATURE_26": "unset",
                        "UI_ENABLE_MLPF_FEATURE_27": "unset",
                        "UI_ENABLE_MLPF_FEATURE_28": "unset",
                        "UI_ENABLE_MLPF_FEATURE_29": "unset",
                        "UI_ENABLE_MLPF_FEATURE_30": "unset",
                        "UI_ENABLE_MLPF_FEATURE_31": "unset",
                        "UI_ENABLE_MLPF_FEATURE_32": "unset",
                        "UI_ENABLE_MLPF_FEATURE_33": "unset",
                        "UI_ENABLE_MLPF_FEATURE_34": "unset",
                        "UI_ENABLE_MLPF_FEATURE_35": "unset",
                        "UI_ENABLE_MLPF_FEATURE_36": "unset",
                        "UI_ENABLE_MLPF_FEATURE_37": "unset",
                        "UI_ENABLE_MLPF_FEATURE_38": "unset",
                        "UI_ENABLE_MLPF_FEATURE_39": "unset",
                        "UI_ENABLE_MLPF_FEATURE_40": "unset",
                        "UI_ENABLE_MLPF_FEATURE_41": "unset",
                        "UI_ENABLE_MLPF_FEATURE_42": "unset",
                        "UI_ENABLE_MLPF_FEATURE_43": "unset",
                        "UI_ENABLE_MLPF_FEATURE_44": "unset",
                        "UI_ENABLE_MLPF_FEATURE_45": "unset",
                        "UI_ENABLE_MLPF_FEATURE_46": "unset",
                        "UI_ENABLE_MLPF_FEATURE_47": "unset",
                        "UI_ENABLE_MLPF_FEATURE_48": "unset",
                        "UI_ENABLE_MLPF_FEATURE_49": "unset",
                        "UI_ENABLE_MLPF_FEATURE_50": "unset",
                        "UI_ENABLE_MODIFY_LISTING_AUTO_FULFILLMENT_REPLICATION_REFRESH_SCHEDULE": "unset",
                        "UI_ENABLE_MONETIZATION_OFFER_AND_PRICING_PLAN_VALIDATIONS": "enabled",
                        "UI_ENABLE_MONITORING_QUERY_INSIGHTS": "DISABLED",
                        "UI_ENABLE_NATIVE_APPS_APP_PACKAGES_IN_UNIVERSAL_SEARCH": "enabled",
                        "UI_ENABLE_NATIVE_APPS_APP_SPEC": "enabled",
                        "UI_ENABLE_NATIVE_APPS_APP_SPEC_SI_TYPE": "enabled",
                        "UI_ENABLE_NATIVE_APPS_AUTOFULFILLMENT_IN_AWS": "enabled",
                        "UI_ENABLE_NATIVE_APPS_AUTOFULFILLMENT_IN_AZURE": "ENABLED",
                        "UI_ENABLE_NATIVE_APPS_AUTOFULFILLMENT_IN_GCP": "ENABLED",
                        "UI_ENABLE_NATIVE_APPS_BUDGETS_EXPERIENCE_IMPROVEMENTS": "enabled",
                        "UI_ENABLE_NATIVE_APPS_BUDGETS_INTEGRATION": "enabled",
                        "UI_ENABLE_NATIVE_APPS_CONFIGURATION_REDIRECT_REDESIGN": "enabled",
                        "UI_ENABLE_NATIVE_APPS_CVE": "unset",
                        "UI_ENABLE_NATIVE_APPS_DELAYED_UPGRADES": "unset",
                        "UI_ENABLE_NATIVE_APPS_EMPTY_APPS_STATE": "enabled",
                        "UI_ENABLE_NATIVE_APPS_EVENT_SHARING_V2": "enabled",
                        "UI_ENABLE_NATIVE_APPS_EXTERNAL_ACCESS": "ENABLED",
                        "UI_ENABLE_NATIVE_APPS_EXTERNAL_ACCESS_ICONS": "enabled",
                        "UI_ENABLE_NATIVE_APPS_EXTERNAL_ACCESS_V2": "enabled",
                        "UI_ENABLE_NATIVE_APPS_MULTI_APP_INSTANCES_FOR_FREE_LISTINGS": "enabled",
                        "UI_ENABLE_NATIVE_APPS_PERFORMANCE_IMPROVEMENTS": "unset",
                        "UI_ENABLE_NATIVE_APPS_RCR_PHASE_ONE": "enabled",
                        "UI_ENABLE_NATIVE_APPS_RELEASE_CHANNELS": "ENABLED",
                        "UI_ENABLE_NATIVE_APPS_RELEASE_CHANNELS_PROVIDER_SIDE": "unset",
                        "UI_ENABLE_NATIVE_APPS_REQUEST_ACCESS": "unset",
                        "UI_ENABLE_NATIVE_APPS_SIMPLIFIED_APPS_CONSUMER": "unset",
                        "UI_ENABLE_NATIVE_APPS_SNOWSIGHT_DRIVEN_CONFIGURATION_M1": "enabled",
                        "UI_ENABLE_NATIVE_APPS_TELEMETRY_LEVELS": "enabled",
                        "UI_ENABLE_NATIVE_APPS_TELEMETRY_ON_EVENT_DEPRECATION": "enabled",
                        "UI_ENABLE_NATIVE_APP_DETAILS_IN_DATABASE_PAGE": "enabled",
                        "UI_ENABLE_NAX_FEATURE_001": "unset",
                        "UI_ENABLE_NAX_FEATURE_002": "enabled",
                        "UI_ENABLE_NAX_FEATURE_003": "unset",
                        "UI_ENABLE_NAX_FEATURE_004": "unset",
                        "UI_ENABLE_NAX_FEATURE_005": "unset",
                        "UI_ENABLE_NAX_FEATURE_006": "unset",
                        "UI_ENABLE_NAX_FEATURE_007": "unset",
                        "UI_ENABLE_NAX_FEATURE_008": "unset",
                        "UI_ENABLE_NAX_FEATURE_009": "unset",
                        "UI_ENABLE_NAX_FEATURE_010": "unset",
                        "UI_ENABLE_NAX_FEATURE_011": "unset",
                        "UI_ENABLE_NAX_FEATURE_012": "unset",
                        "UI_ENABLE_NAX_FEATURE_013": "unset",
                        "UI_ENABLE_NAX_FEATURE_014": "unset",
                        "UI_ENABLE_NAX_FEATURE_015": "unset",
                        "UI_ENABLE_NAX_FEATURE_016": "unset",
                        "UI_ENABLE_NAX_FEATURE_017": "unset",
                        "UI_ENABLE_NAX_FEATURE_018": "unset",
                        "UI_ENABLE_NAX_FEATURE_019": "unset",
                        "UI_ENABLE_NAX_FEATURE_020": "unset",
                        "UI_ENABLE_NAX_FEATURE_021": "unset",
                        "UI_ENABLE_NAX_FEATURE_022": "unset",
                        "UI_ENABLE_NAX_FEATURE_023": "unset",
                        "UI_ENABLE_NAX_FEATURE_024": "unset",
                        "UI_ENABLE_NAX_FEATURE_025": "unset",
                        "UI_ENABLE_NAX_FEATURE_026": "unset",
                        "UI_ENABLE_NAX_FEATURE_027": "unset",
                        "UI_ENABLE_NAX_FEATURE_028": "unset",
                        "UI_ENABLE_NAX_FEATURE_029": "unset",
                        "UI_ENABLE_NAX_FEATURE_030": "unset",
                        "UI_ENABLE_NAX_FEATURE_031": "unset",
                        "UI_ENABLE_NAX_FEATURE_032": "unset",
                        "UI_ENABLE_NAX_FEATURE_033": "unset",
                        "UI_ENABLE_NAX_FEATURE_034": "unset",
                        "UI_ENABLE_NAX_FEATURE_035": "unset",
                        "UI_ENABLE_NAX_FEATURE_036": "unset",
                        "UI_ENABLE_NAX_FEATURE_037": "unset",
                        "UI_ENABLE_NAX_FEATURE_038": "unset",
                        "UI_ENABLE_NAX_FEATURE_039": "unset",
                        "UI_ENABLE_NAX_FEATURE_040": "unset",
                        "UI_ENABLE_NAX_FEATURE_041": "unset",
                        "UI_ENABLE_NAX_FEATURE_042": "unset",
                        "UI_ENABLE_NAX_FEATURE_043": "unset",
                        "UI_ENABLE_NAX_FEATURE_044": "unset",
                        "UI_ENABLE_NAX_FEATURE_045": "unset",
                        "UI_ENABLE_NAX_FEATURE_046": "unset",
                        "UI_ENABLE_NAX_FEATURE_047": "unset",
                        "UI_ENABLE_NAX_FEATURE_048": "unset",
                        "UI_ENABLE_NAX_FEATURE_049": "unset",
                        "UI_ENABLE_NAX_FEATURE_050": "unset",
                        "UI_ENABLE_NAX_FEATURE_051": "unset",
                        "UI_ENABLE_NAX_FEATURE_052": "unset",
                        "UI_ENABLE_NAX_FEATURE_053": "unset",
                        "UI_ENABLE_NAX_FEATURE_054": "unset",
                        "UI_ENABLE_NAX_FEATURE_055": "unset",
                        "UI_ENABLE_NAX_FEATURE_056": "unset",
                        "UI_ENABLE_NAX_FEATURE_057": "unset",
                        "UI_ENABLE_NAX_FEATURE_058": "unset",
                        "UI_ENABLE_NAX_FEATURE_059": "unset",
                        "UI_ENABLE_NAX_FEATURE_060": "unset",
                        "UI_ENABLE_NAX_FEATURE_061": "unset",
                        "UI_ENABLE_NAX_FEATURE_062": "unset",
                        "UI_ENABLE_NAX_FEATURE_063": "unset",
                        "UI_ENABLE_NAX_FEATURE_064": "unset",
                        "UI_ENABLE_NAX_FEATURE_065": "unset",
                        "UI_ENABLE_NAX_FEATURE_066": "unset",
                        "UI_ENABLE_NAX_FEATURE_067": "unset",
                        "UI_ENABLE_NAX_FEATURE_068": "unset",
                        "UI_ENABLE_NAX_FEATURE_069": "unset",
                        "UI_ENABLE_NAX_FEATURE_070": "unset",
                        "UI_ENABLE_NAX_FEATURE_071": "unset",
                        "UI_ENABLE_NAX_FEATURE_072": "unset",
                        "UI_ENABLE_NAX_FEATURE_073": "unset",
                        "UI_ENABLE_NAX_FEATURE_074": "unset",
                        "UI_ENABLE_NAX_FEATURE_075": "unset",
                        "UI_ENABLE_NAX_FEATURE_076": "unset",
                        "UI_ENABLE_NAX_FEATURE_077": "unset",
                        "UI_ENABLE_NAX_FEATURE_078": "unset",
                        "UI_ENABLE_NAX_FEATURE_079": "unset",
                        "UI_ENABLE_NAX_FEATURE_080": "unset",
                        "UI_ENABLE_NAX_FEATURE_081": "unset",
                        "UI_ENABLE_NAX_FEATURE_082": "unset",
                        "UI_ENABLE_NAX_FEATURE_083": "unset",
                        "UI_ENABLE_NAX_FEATURE_084": "unset",
                        "UI_ENABLE_NAX_FEATURE_085": "unset",
                        "UI_ENABLE_NAX_FEATURE_086": "unset",
                        "UI_ENABLE_NAX_FEATURE_087": "unset",
                        "UI_ENABLE_NAX_FEATURE_088": "unset",
                        "UI_ENABLE_NAX_FEATURE_089": "unset",
                        "UI_ENABLE_NAX_FEATURE_090": "unset",
                        "UI_ENABLE_NAX_FEATURE_091": "unset",
                        "UI_ENABLE_NAX_FEATURE_092": "unset",
                        "UI_ENABLE_NAX_FEATURE_093": "unset",
                        "UI_ENABLE_NAX_FEATURE_094": "unset",
                        "UI_ENABLE_NAX_FEATURE_095": "unset",
                        "UI_ENABLE_NAX_FEATURE_096": "unset",
                        "UI_ENABLE_NAX_FEATURE_097": "unset",
                        "UI_ENABLE_NAX_FEATURE_098": "unset",
                        "UI_ENABLE_NAX_FEATURE_099": "unset",
                        "UI_ENABLE_NAX_FEATURE_100": "unset",
                        "UI_ENABLE_NAX_FEATURE_101": "unset",
                        "UI_ENABLE_NAX_FEATURE_102": "unset",
                        "UI_ENABLE_NAX_FEATURE_103": "unset",
                        "UI_ENABLE_NAX_FEATURE_104": "unset",
                        "UI_ENABLE_NAX_FEATURE_105": "unset",
                        "UI_ENABLE_NAX_FEATURE_106": "unset",
                        "UI_ENABLE_NAX_FEATURE_107": "unset",
                        "UI_ENABLE_NAX_FEATURE_108": "unset",
                        "UI_ENABLE_NAX_FEATURE_109": "unset",
                        "UI_ENABLE_NAX_FEATURE_110": "unset",
                        "UI_ENABLE_NAX_FEATURE_111": "unset",
                        "UI_ENABLE_NAX_FEATURE_112": "unset",
                        "UI_ENABLE_NAX_FEATURE_113": "unset",
                        "UI_ENABLE_NAX_FEATURE_114": "unset",
                        "UI_ENABLE_NAX_FEATURE_115": "unset",
                        "UI_ENABLE_NAX_FEATURE_116": "unset",
                        "UI_ENABLE_NAX_FEATURE_117": "unset",
                        "UI_ENABLE_NAX_FEATURE_118": "unset",
                        "UI_ENABLE_NAX_FEATURE_119": "unset",
                        "UI_ENABLE_NAX_FEATURE_120": "unset",
                        "UI_ENABLE_NAX_FEATURE_121": "unset",
                        "UI_ENABLE_NAX_FEATURE_122": "unset",
                        "UI_ENABLE_NAX_FEATURE_123": "unset",
                        "UI_ENABLE_NAX_FEATURE_124": "unset",
                        "UI_ENABLE_NAX_FEATURE_125": "unset",
                        "UI_ENABLE_NAX_FEATURE_126": "unset",
                        "UI_ENABLE_NAX_FEATURE_127": "unset",
                        "UI_ENABLE_NAX_FEATURE_128": "unset",
                        "UI_ENABLE_NAX_FEATURE_129": "unset",
                        "UI_ENABLE_NAX_FEATURE_130": "unset",
                        "UI_ENABLE_NAX_FEATURE_131": "unset",
                        "UI_ENABLE_NAX_FEATURE_132": "unset",
                        "UI_ENABLE_NAX_FEATURE_133": "unset",
                        "UI_ENABLE_NAX_FEATURE_134": "unset",
                        "UI_ENABLE_NAX_FEATURE_135": "unset",
                        "UI_ENABLE_NAX_FEATURE_136": "unset",
                        "UI_ENABLE_NAX_FEATURE_137": "unset",
                        "UI_ENABLE_NAX_FEATURE_138": "unset",
                        "UI_ENABLE_NAX_FEATURE_139": "unset",
                        "UI_ENABLE_NAX_FEATURE_140": "unset",
                        "UI_ENABLE_NAX_FEATURE_141": "unset",
                        "UI_ENABLE_NAX_FEATURE_142": "unset",
                        "UI_ENABLE_NAX_FEATURE_143": "unset",
                        "UI_ENABLE_NAX_FEATURE_144": "unset",
                        "UI_ENABLE_NAX_FEATURE_145": "unset",
                        "UI_ENABLE_NAX_FEATURE_146": "unset",
                        "UI_ENABLE_NAX_FEATURE_147": "unset",
                        "UI_ENABLE_NAX_FEATURE_148": "unset",
                        "UI_ENABLE_NAX_FEATURE_149": "unset",
                        "UI_ENABLE_NAX_FEATURE_150": "unset",
                        "UI_ENABLE_NAX_REINSTALL_TRANSFER_OWNERSHIP": false,
                        "UI_ENABLE_NAX_UNINSTALL_TRANSFER_OWNERSHIP": true,
                        "UI_ENABLE_NEW_TARGETING_EXPERIENCE_FOR_INTRA_ORG": "enabled",
                        "UI_ENABLE_NOTEBOOK": "enabled",
                        "UI_ENABLE_NOTEBOOKS_WH_PYTHON_ENVIRONMENT": "enabled",
                        "UI_ENABLE_NOTEBOOK_CELL_STATUS": "enabled",
                        "UI_ENABLE_NOTEBOOK_CONTAINER_INSTALL_FROM_STAGE": "enabled",
                        "UI_ENABLE_NOTEBOOK_DATA_SCIENCE_AGENT": "DISABLED",
                        "UI_ENABLE_NOTEBOOK_DATA_SCIENCE_AGENT_PRPR": null,
                        "UI_ENABLE_NOTEBOOK_DEFAULT_WAREHOUSE": "enabled",
                        "UI_ENABLE_NOTEBOOK_DISABLED_CREATE_NOTEBOOK_FOR_PRIVATELINK": "disabled",
                        "UI_ENABLE_NOTEBOOK_DISABLED_SPCS_EXPERIENCE_FOR_ADMINS": "disabled",
                        "UI_ENABLE_NOTEBOOK_EXECUTION_ARGS": "ENABLED",
                        "UI_ENABLE_NOTEBOOK_EXECUTION_CANCELLATION": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_0": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_1": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_10": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_12": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_13": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_14": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_15": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_16": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_17": "DISABLED",
                        "UI_ENABLE_NOTEBOOK_FEATURE_18": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_19": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_2": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_20": "disabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_21": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_22": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_24": "ENABLED",
                        "UI_ENABLE_NOTEBOOK_FEATURE_25": "ENABLED",
                        "UI_ENABLE_NOTEBOOK_FEATURE_26": "ENABLED",
                        "UI_ENABLE_NOTEBOOK_FEATURE_27": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_28": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_30": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_32": "ENABLED",
                        "UI_ENABLE_NOTEBOOK_FEATURE_33": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_34": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_35": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_36": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_37": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_38": "ENABLED",
                        "UI_ENABLE_NOTEBOOK_FEATURE_4": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_40": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_41": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_42": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_43": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_44": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_45": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_46": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_47": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_48": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_49": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_5": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_50": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_51": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_52": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_53": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_54": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_55": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_56": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_57": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_58": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_59": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_6": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_60": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_61": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_62": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_63": "ENABLED",
                        "UI_ENABLE_NOTEBOOK_FEATURE_64": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_65": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_66": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_68": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_69": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_7": "unset",
                        "UI_ENABLE_NOTEBOOK_FEATURE_70": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_8": "enabled",
                        "UI_ENABLE_NOTEBOOK_FEATURE_9": "unset",
                        "UI_ENABLE_NOTEBOOK_INCREMENTAL_PACKAGE_INSTALLATION": "unset",
                        "UI_ENABLE_NOTEBOOK_IN_PROVIDER_EXPERIENCE": "unset",
                        "UI_ENABLE_NOTEBOOK_JUPYTER_VARIABLE_SEMANTICS": "enabled",
                        "UI_ENABLE_NOTEBOOK_LIST_VIEW_FILTERING": "enabled",
                        "UI_ENABLE_NOTEBOOK_SELECT_MULTIPLE_CELLS": "unset",
                        "UI_ENABLE_NOTEBOOK_SINGLE_IFRAME": "unset",
                        "UI_ENABLE_NOTEBOOK_STATUS_COLUMN": null,
                        "UI_ENABLE_NO_WAREHOUSE_CALL_TO_ACTION": "enabled",
                        "UI_ENABLE_NPS": true,
                        "UI_ENABLE_OE_MULTIPANE": true,
                        "UI_ENABLE_OMIT_HOMEPAGE_CONSUMER_LISTING_LOAD": "unset",
                        "UI_ENABLE_OPENFLOW_FEATURE_1": false,
                        "UI_ENABLE_OPENFLOW_IN_SNOWSIGHT": true,
                        "UI_ENABLE_ORGANIZATIONAL_MARKETPLACE": "enabled",
                        "UI_ENABLE_ORG_LEVEL_TAGGING": "unset",
                        "UI_ENABLE_PAYMENT_METHOD_SELECTION": "unset",
                        "UI_ENABLE_PER_CUSTOMER_PRICING_CONSUMER_EXPERIENCE": "enabled",
                        "UI_ENABLE_PER_CUSTOMER_PRICING_PROVIDER_EXPERIENCE": "unset",
                        "UI_ENABLE_PER_CUSTOMER_PRICING_PROVIDER_EXPERIENCE_PRPRV2": "unset",
                        "UI_ENABLE_PRIVATE_LISTINGS": true,
                        "UI_ENABLE_PROGRAMMATIC_LISTING_ACCESS": false,
                        "UI_ENABLE_PROVIDER_FEATURE_TRIAL_DETAILS_IN_LISTING_API": "enabled",
                        "UI_ENABLE_PUBLIC_LISTINGS": true,
                        "UI_ENABLE_PUBLIC_LISTINGS_IN_VPS": "disabled",
                        "UI_ENABLE_QUERY_RESULT_PAGINATION": "unset",
                        "UI_ENABLE_READ_ONLY_AGG_PROJ_POLICIES": "ENABLED",
                        "UI_ENABLE_RELAX_CONSUMER_TOS_REQUIREMENT": "enabled",
                        "UI_ENABLE_RELAX_CONSUMER_TOS_REQUIREMENT_V2": "enabled",
                        "UI_ENABLE_RELAX_PROVIDER_TOS_REQUIREMENT": "enabled",
                        "UI_ENABLE_RELAX_PROVIDER_TOS_REQUIREMENT_V2": "unset",
                        "UI_ENABLE_REMAINING_TRIAL_USAGE_TILE": true,
                        "UI_ENABLE_RTKQ_MIGRATION_FOR_SUPPORT": "enabled",
                        "UI_ENABLE_S8_CLASSIC_CONSOLE_ACCESS": false,
                        "UI_ENABLE_SECONDARY_ROLES_IN_PROVIDER_STUDIO": "enabled",
                        "UI_ENABLE_SEPARATE_BILLING_TERMS_PAGES": "unset",
                        "UI_ENABLE_SESSION_INTEGRATION": true,
                        "UI_ENABLE_SNOWSCOPE_FEATURE_06": "enabled",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_07": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_08": "enabled",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_09": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_10": "DISABLED",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_11": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_12": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_13": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_14": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_15": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_16": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_17": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_18": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_19": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_20": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_21": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_22": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_23": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_24": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_25": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_26": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_27": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_28": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_29": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_30": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_31": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_32": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_33": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_34": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_35": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_36": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_37": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_38": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_39": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_40": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_41": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_42": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_43": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_44": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_45": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_46": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_47": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_48": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_49": "unset",
                        "UI_ENABLE_SNOWSCOPE_FEATURE_50": "unset",
                        "UI_ENABLE_SNOWSIGHT_ADMIN_WAREHOUSES_FEATURE_PROMO_SECTION": true,
                        "UI_ENABLE_SNOWSIGHT_HOMEPAGE_FEATURE_PROMO_SECTION": true,
                        "UI_ENABLE_SQL_EXAMPLES_FOR_INTRA_ORG": "enabled",
                        "UI_ENABLE_STAGE_FILES_EXPLORER": "unset",
                        "UI_ENABLE_STAGE_FILES_UPLOAD_DOWNLOAD": "unset",
                        "UI_ENABLE_STREAMLIT_APP_STATE_MACHINE": "disabled",
                        "UI_ENABLE_STREAMLIT_BATCH_TELEMETRY_ENDPOINT": "unset",
                        "UI_ENABLE_STREAMLIT_DEFAULT_WAREHOUSE": "unset",
                        "UI_ENABLE_STREAMLIT_DISPLAY_SPCS_DETAILED_LOAD_SEQUENCE": "unset",
                        "UI_ENABLE_STREAMLIT_EMBEDDED_STAGES": "unset",
                        "UI_ENABLE_STREAMLIT_HIDE_PREVIEW_PILL": "enabled",
                        "UI_ENABLE_STREAMLIT_INDIVIDUAL_LOAD_METRICS": "unset",
                        "UI_ENABLE_STREAMLIT_IN_SNOWFLAKE": "enabled",
                        "UI_ENABLE_STREAMLIT_NEW_MENU_BUTTON": "enabled",
                        "UI_ENABLE_STREAMLIT_ON_SPCS": "unset",
                        "UI_ENABLE_STREAMLIT_PIPE_TELEMETRY_TO_BATCH_ENDPOINT": "unset",
                        "UI_ENABLE_STREAMLIT_PREAMBLE_CSP_VIOLATION_DETECTION": "unset",
                        "UI_ENABLE_STREAMLIT_PREAMBLE_SERVICE_WORKER": "unset",
                        "UI_ENABLE_STREAMLIT_RESTRICTED_CALLERS_RIGHTS": "unset",
                        "UI_ENABLE_SUPPORT": true,
                        "UI_ENABLE_SUPPORT_FEATURE_3": "enabled",
                        "UI_ENABLE_SUPPORT_FEATURE_5": "enabled",
                        "UI_ENABLE_SUPPORT_FEATURE_6": "enabled",
                        "UI_ENABLE_SUPPORT_FEATURE_8": "unset",
                        "UI_ENABLE_SUPPORT_FEATURE_9": "enabled",
                        "UI_ENABLE_SUPPORT_FOR_TRIAL_ACCOUNTS": true,
                        "UI_ENABLE_SUPPORT_SUGGESTIONS_IN_MODAL": "enabled",
                        "UI_ENABLE_TAG_BASED_BUDGETS": "ENABLED",
                        "UI_ENABLE_TASKS_SUSPEND_RESUME": "unset",
                        "UI_ENABLE_TRIAL_HOME_PAGE": false,
                        "UI_ENABLE_TRIAL_HOME_PAGE_V2": false,
                        "UI_ENABLE_TRIAL_NEW_USER_BASIC_ONBOARDING": true,
                        "UI_ENABLE_TRUST_CENTER_ANOMALIES_TAB": null,
                        "UI_ENABLE_TRUST_CENTER_EXTENSIBILITY": "DISABLED",
                        "UI_ENABLE_TRUST_CENTER_ORG_VIEW": "DISABLED",
                        "UI_ENABLE_TUTORIALS_PAGE": true,
                        "UI_ENABLE_UI_PLATFORM_FEATURE_13": null,
                        "UI_ENABLE_UI_PLATFORM_FEATURE_14": null,
                        "UI_ENABLE_UI_PLATFORM_FEATURE_16": "enabled",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_26": null,
                        "UI_ENABLE_UI_PLATFORM_FEATURE_29": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_3": "ENABLED",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_31": null,
                        "UI_ENABLE_UI_PLATFORM_FEATURE_32": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_33": null,
                        "UI_ENABLE_UI_PLATFORM_FEATURE_35": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_38": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_39": "enabled",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_40": null,
                        "UI_ENABLE_UI_PLATFORM_FEATURE_41": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_43": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_44": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_45": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_46": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_47": "ENABLED",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_5": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_50": "enabled",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_7": "unset",
                        "UI_ENABLE_UI_PLATFORM_FEATURE_8": "unset",
                        "UI_ENABLE_UNIFIED_MARKETPLACE_M1": "enabled",
                        "UI_ENABLE_UNIFIED_MARKETPLACE_M1_FILTER": "enabled",
                        "UI_ENABLE_UNIFIED_MARKETPLACE_M1_SEARCH_CARDS": "enabled",
                        "UI_ENABLE_UNIFIED_MARKETPLACE_M3": "unset",
                        "UI_ENABLE_UNIFIED_MARKETPLACE_PRIVATE_LISTINGS": "enabled",
                        "UI_ENABLE_USAGE_CURRENCY_SQL_FIX": "unset",
                        "UI_ENABLE_USER_OAUTH_FLOW_NUWEB": "enabled",
                        "UI_ENABLE_USE_SECONDARY_ROLES_SWITCH": "disabled",
                        "UI_ENABLE_WAW_DEX_FEATURE_1": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_11": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_12": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_13": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_14": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_15": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_16": "ENABLED",
                        "UI_ENABLE_WAW_DEX_FEATURE_19": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_2": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_20": "enabled",
                        "UI_ENABLE_WAW_DEX_FEATURE_21": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_22": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_23": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_24": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_25": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_26": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_27": "enabled",
                        "UI_ENABLE_WAW_DEX_FEATURE_28": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_29": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_30": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_31": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_32": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_33": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_34": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_35": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_36": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_37": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_38": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_39": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_4": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_40": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_41": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_42": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_43": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_44": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_45": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_46": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_47": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_48": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_49": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_5": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_50": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_6": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_7": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_8": "unset",
                        "UI_ENABLE_WAW_DEX_FEATURE_9": "unset",
                        "UI_ENABLE_WORKSHEET_TUTORIAL_CARDS": true,
                        "UI_FEATURE_APPS_CONSUMER_EXPERIENCE": "enabled",
                        "UI_FEATURE_APPS_MANAGEMENT_EXPERIENCE": "enabled",
                        "UI_FEATURE_APPS_PACKAGE_MANAGEMENT_EXPERIENCE": "enabled",
                        "UI_FEATURE_APPS_PROVIDER_EXPERIENCE": "enabled",
                        "UI_FEATURE_AUTOMATED_MONETIZATION_ONBOARDING_ENABLED": true,
                        "UI_FEATURE_CONSUMER_MONETIZATION_ENABLED": false,
                        "UI_FEATURE_CONSUMER_MONETIZATION_ONBOARDING_ENABLED": true,
                        "UI_FEATURE_DATA_EXCHANGE_ENABLED": true,
                        "UI_FEATURE_DATA_SHARING_ENABLED": true,
                        "UI_FEATURE_ENABLE_DOCUMENT_AI_QUOTED_IDENTIFIERS": "unset",
                        "UI_FEATURE_ENTITY_KEY_IN_AGGREGATION_CONSTRAINTS": "unset",
                        "UI_FEATURE_PARTNER_CONNECT_ENABLED": true,
                        "UI_FEATURE_PRIVACY_POLICIES_IN_DATA_CLEAN_ROOM": "unset",
                        "UI_FEATURE_PRIVATE_DATA_EXCHANGE_ENABLED": true,
                        "UI_FEATURE_PROVIDER_AUTOMATED_MONETIZATION_ONBOARDING_ENABLED": false,
                        "UI_FEATURE_PROVIDER_MONETIZATION_ENABLED": true,
                        "UI_FEATURE_PROVIDER_MONETIZATION_ONBOARDING_ENABLED": true,
                        "UI_FEATURE_PROVIDER_MONETIZATION_PRICING_CHANGES_ENABLED": true,
                        "UI_FEATURE_PROVIDER_STUDIO_CONVERSION_METRIC_ENABLED": "unset",
                        "UI_FEATURE_PROVIDER_STUDIO_HOME_TRENDS_SECTION": "unset",
                        "UI_FEATURE_PROVIDER_STUDIO_NEW_ANALYTICS_ENABLED": "unset",
                        "UI_IMPORTED_DATABASE_TTL_MS": 0,
                        "UI_LANDING_PAGE": "SNOWFLAKE_APP",
                        "UI_LANDING_PAGE_OVERRIDE": "SNOWFLAKE_APP",
                        "UI_LASIK_C4J1_BEHAVIOR_CHANGE_1": true,
                        "UI_MIGRATION_BCR_REMOVE_CLASSIC_CONSOLE_ACCESS": false,
                        "UI_MIGRATION_REMOVE_CLASSIC_CONSOLE_ACCESS_ENABLED": true,
                        "UI_NEW_BILLING_ENABLED": true,
                        "UI_NOTEBOOK_NONINTERACTIVE_JOB_RESULT_HISTORY_TIME": 1209600,
                        "UI_NOTEBOOK_RUNTIME_VERSIONS": "3.9-1.0",
                        "UI_RATE_LIMITER_RPS": 20,
                        "UI_READER_ACCOUNTS_TIME_UNTIL_ACTIVE": 30000,
                        "UI_SECURABLE_OPERATION_AUTHORIZATION": "enabled",
                        "UI_SNOWSIGHT_BLOCK_UNSANITIZED_URLS": false,
                        "UI_SNOWSIGHT_ENABLE_COLUMN_RESIZING": "unset",
                        "UI_SNOWSIGHT_ENABLE_GEOSPATIAL_VISUALIZATION": "unset",
                        "UI_SNOWSIGHT_ENABLE_POST_API_SUPPORT": "unset",
                        "UI_SNOWSIGHT_ENABLE_REGIONLESS_REDIRECT": true,
                        "UI_SNOWSIGHT_ENABLE_USERNAME_ONLY_DISPLAY": "unset",
                        "UI_SNOWSIGHT_PINNED_VERSION": "",
                        "UI_STREAMLIT_APP_HEARTBEAT_FREQUENCY_SECONDS": 60,
                        "UI_STREAMLIT_SLEEP_TIMEOUT_SECONDS_DEFAULT": 900,
                        "UI_STREAMLIT_SLEEP_TIMEOUT_SECONDS_MAX": 14400,
                        "UI_STREAMLIT_SLEEP_TIMEOUT_SECONDS_MIN": 300,
                        "UI_USAGE_STORAGE_MAX_OBJECTS": 5000,
                        "UI_USER_INPUT_DETECTION_DEBOUNCE_INTERVAL_MS": 2000,
                        "UI_USE_CUSTOM_MONETIZATION_FEE_SCHEDULE": false,
                        "UI_USE_SHOW_ACCOUNTS": "enabled",
                        "USER_TASK_ENABLE_SCHEDULING_MODE_DDL": false,
                        "USE_ORG_AND_ALIAS_FOR_DX": true,
                        "VERSION_ACCOUNT_USAGE_COMPLETE_TASK_GRAPHS": 13,
                        "VERSION_ACCOUNT_USAGE_COPY_HISTORY": 10,
                        "VERSION_ACCOUNT_USAGE_DATABASE_STORAGE_USAGE_HISTORY": 9,
                        "VERSION_ACCOUNT_USAGE_METERING_DAILY_HISTORY": 61,
                        "VERSION_ACCOUNT_USAGE_METERING_HISTORY": 55,
                        "VERSION_ACCOUNT_USAGE_STORAGE_USAGE": 4,
                        "VERSION_ACCOUNT_USAGE_TASK_HISTORY": 26,
                        "VERSION_DATA_SHARING_USAGE_LAF_METERING_DAILY": **********,
                        "VERSION_DATA_SHARING_USAGE_LAF_REFRESH_DAILY": 2,
                        "VERSION_DATA_SHARING_USAGE_LISTING_ACCESS_HISTORY": 8,
                        "VERSION_DATA_SHARING_USAGE_LISTING_CONSUMPTION_DAILY": 8,
                        "VERSION_DATA_SHARING_USAGE_LISTING_EVENTS_DAILY": 15,
                        "VERSION_DATA_SHARING_USAGE_LISTING_TELEMETRY_DAILY": 7,
                        "VERSION_ORGANIZATION_USAGE_DATABASE_STORAGE_USAGE_HISTORY": 10,
                        "WAREHOUSE_MAX_SIZE": "6X-Large",
                        "WWW_SNOWFLAKE_BASE_URL": "https://www.snowflake.com",
                        "X3LARGE_WAREHOUSE_CLUSTER_COUNT_LIMIT": 20,
                        "X4LARGE_WAREHOUSE_CLUSTER_COUNT_LIMIT": 10,
                        "X5LARGE_WAREHOUSE_CLUSTER_COUNT_LIMIT": 10,
                        "X6LARGE_WAREHOUSE_CLUSTER_COUNT_LIMIT": 10,
                        "XLARGE_WAREHOUSE_CLUSTER_COUNT_LIMIT": 80,
                        "XSMALL_WAREHOUSE_CLUSTER_COUNT_LIMIT": 300,
                        "XXLARGE_WAREHOUSE_CLUSTER_COUNT_LIMIT": 40,
                        "_DP_COPILOT_MARKETPLACE_MESSAGE_LIMIT": 10,
                        "_DP_CORTEX_SEARCH_ADMIN_UI_DETAILS_PAGE_PEP": false,
                        "_DP_CORTEX_SEARCH_ADMIN_UI_MAIN_PAGE_PEP": false,
                        "_DP_CORTEX_SEARCH_ADMIN_UI_PLAYGROUND_PEP": false,
                        "_DP_DEX_UI_ENABLE_OUTBOUND_PRIVATE_LINK": true,
                        "_DP_DEX_UI_ENABLE_SECURABLE_AUTHORIZATION": false,
                        "_DP_DISABLE_DEFAULT_REQUEST_CONTEXT_FOR_STAGE_FILE_OPERATIONS": false,
                        "_DP_DISABLE_WORKSPACES_IFRAME": false,
                        "_DP_ENABLE_AI_OBS_RUN_STATUS_UPDATE_TIMEOUT_HANDLING": true,
                        "_DP_ENABLE_ATC_DISMISSAL_COOLDOWN": true,
                        "_DP_ENABLE_ATC_SESSION_COOLDOWN": true,
                        "_DP_ENABLE_ATC_TIP_WEIGHTAGE": true,
                        "_DP_ENABLE_BOTTOM_UP_SHOW_HOMEPAGE": false,
                        "_DP_ENABLE_BOTTOM_UP_SHOW_NOTEBOOKS_STREAMLITS": false,
                        "_DP_ENABLE_DATA_QUALITY_REDIRECT": false,
                        "_DP_ENABLE_FEATURED_TEMPLATES": false,
                        "_DP_ENABLE_FEATURED_TEMPLATES_ON_NOTEBOOKS": true,
                        "_DP_ENABLE_FEATURED_TEMPLATES_ON_WORKSHEETS": true,
                        "_DP_ENABLE_FETCH_PROMOTIONS_FROM_WINTERFEST_USER_LEVEL": false,
                        "_DP_ENABLE_GS_SYS_FUNC_GET_TARGETED_PROMOTIONS": false,
                        "_DP_ENABLE_HISTORY_MARKETPLACE_COPILOT": true,
                        "_DP_ENABLE_MIGRATIONS_HUB": true,
                        "_DP_ENABLE_ML_OBS_DASHBOARD_FROM_MONITOR": false,
                        "_DP_ENABLE_ML_OBS_NEW_DASHBOARD_DIALOG": false,
                        "_DP_ENABLE_ML_OBS_SEGMENTS": false,
                        "_DP_ENABLE_ML_OBS_TASK_FROM_MONITOR": true,
                        "_DP_ENABLE_PROMPT_GENERATION_LLM_PLAYGROUND": false,
                        "_DP_ENABLE_SEARCH_AND_FILTER_ON_TEMPLATES_HUB": true,
                        "_DP_ENABLE_SNOWPARK_OBSERVABILITY_BANNER_USER_LEVEL": false,
                        "_DP_ENABLE_SNOWPARK_OBSERVABILITY_USER_LEVEL_EXPERIMENT": false,
                        "_DP_ENABLE_STANDARD_FEATURE_PROMO_BANNER": false,
                        "_DP_ENABLE_STANDARD_FEATURE_PROMO_BANNER_ON_HOME": false,
                        "_DP_ENABLE_TEMPLATES_HUB_PUPR": false,
                        "_DP_ENABLE_TIPS_EVENTS_BYPASS": false,
                        "_DP_ENABLE_TIPS_IS_TARGETED_USER_BYPASS": false,
                        "_DP_ENABLE_UNPROVISIONED_WINTERFEST_CONFIGS": false,
                        "_DP_ENABLE_WINTERFEST_PROMOTIONS": false,
                        "_DP_ENABLE_WORKSPACE_CORTEX_AGENT_COMPLETE": true,
                        "_DP_GET_BILLING_ENTITIES_FOR_CURRENT_ORG_ENABLE": false,
                        "_DP_HORIZON_DATABASE_HEADER": true,
                        "_DP_IM_PROFILE_UI_GA": false,
                        "_DP_INVOICES_UI_BILLING_RECREATE_INVALID_STRIPE_CUSTOMER_IDS": true,
                        "_DP_LEFT_NAV_IA_HORIZON": false,
                        "_DP_LEFT_NAV_IA_HORIZON_GA": false,
                        "_DP_LEFT_NAV_IA_HORIZON_USER_LINEAGE": false,
                        "_DP_MX_DATA_QUALITY_UI_PRPR": false,
                        "_DP_MX_DATA_QUALITY_UI_PUPR": false,
                        "_DP_MX_DG_LINEAGE_DELETED_NODES": true,
                        "_DP_MX_GROUPED_QUERY_HISTORY_ENABLE_DETAIL_VIEW_MV": true,
                        "_DP_MX_GROUPED_QUERY_HISTORY_ENABLE_MV_V2": true,
                        "_DP_MX_SENMANTIC_VIEWS_OE": true,
                        "_DP_NOTEBOOK_IMG_REPO_URL": "",
                        "_DP_NOTEBOOK_IMG_TAG": "latest",
                        "_DP_OVERRIDE_MFE_DEBUG_TOOLING_VERSION": "",
                        "_DP_PEP_ACCT_OVERVIEW": false,
                        "_DP_PEP_ADMIN_CONTACTS": false,
                        "_DP_PEP_AGENT_LIVE_TRACE": false,
                        "_DP_PEP_AGENT_MONITORING_TAB": false,
                        "_DP_PEP_AI_EVERYWHERE": false,
                        "_DP_PEP_AI_IDB_MESSAGE_CACHE": true,
                        "_DP_PEP_AI_OBSERVABILITY": false,
                        "_DP_PEP_ANOMALY_DETECTION": false,
                        "_DP_PEP_BUDGETS_BALTO_DATE_RANGE": true,
                        "_DP_PEP_CANARY_TOGGLE": false,
                        "_DP_PEP_COMPUTE_POOLS": false,
                        "_DP_PEP_COPY_HISTORY": false,
                        "_DP_PEP_COPY_HISTORY_EXPERIMENT": true,
                        "_DP_PEP_DYNAMIC_TABLE": false,
                        "_DP_PEP_ENABLE_FETCH_SHOW_DATABASES": false,
                        "_DP_PEP_ENABLE_FETCH_SHOW_LOG_DIFFS": false,
                        "_DP_PEP_ENABLE_FETCH_SHOW_OBJECTS": false,
                        "_DP_PEP_ENABLE_FETCH_SHOW_SCHEMAS": false,
                        "_DP_PEP_ENABLE_FETCH_SHOW_TABLES": false,
                        "_DP_PEP_ENABLE_FETCH_SHOW_TERSE": false,
                        "_DP_PEP_ENABLE_FETCH_SHOW_VIEWS": false,
                        "_DP_PEP_ENABLE_FETCH_SHOW_WAREHOUSES": false,
                        "_DP_PEP_ENABLE_QUERY_CACHE": false,
                        "_DP_PEP_ENABLE_USE_SHOW_TERSE_DATABASES": false,
                        "_DP_PEP_ENABLE_USE_SHOW_TERSE_OBJECTS": false,
                        "_DP_PEP_ENABLE_USE_SHOW_TERSE_WAREHOUSES": false,
                        "_DP_PEP_GROUPED_QUERY_HISTORY": false,
                        "_DP_PEP_GROUPED_QUERY_HISTORY_QUERY_HASH_DETAILS": false,
                        "_DP_PEP_NATIVE_APPS_TABLE": false,
                        "_DP_PEP_NETWORK_CALLS": false,
                        "_DP_PEP_ORG_OVERVIEW": false,
                        "_DP_PEP_PERFORMANCE_EXPLORER": false,
                        "_DP_PEP_PERFORMANCE_EXPLORER_SIDEPANE": false,
                        "_DP_PEP_PLAYGROUND": false,
                        "_DP_PEP_POSTGRES_SERVICES": false,
                        "_DP_PEP_QUERY_HISTORY": false,
                        "_DP_PEP_QUERY_HISTORY_EXPERIMENT": false,
                        "_DP_PEP_RESOURCE_MONITORS": false,
                        "_DP_PEP_SI": true,
                        "_DP_PEP_SI_ADMIN": false,
                        "_DP_PEP_SI_ADMIN_CHAT": false,
                        "_DP_PEP_SI_CORTEX_CLIENT": false,
                        "_DP_PEP_SI_DISABLE_ADVANCED_SETTINGS_MENU": false,
                        "_DP_PEP_SI_DISABLE_SERVER_SIDE_SQL_EXECUTION": false,
                        "_DP_PEP_SI_ENABLE_AGENT_OBJECTS": false,
                        "_DP_PEP_SI_ENABLE_ANALYST_STREAMING": false,
                        "_DP_PEP_SI_ENABLE_AUTO_MODEL_SELECTION": false,
                        "_DP_PEP_SI_ENABLE_FILE_UPLOAD": false,
                        "_DP_PEP_SI_ENABLE_SERVER_SIDE_SQL_EXECUTION": false,
                        "_DP_PEP_SI_ENABLE_THINKING_MODE_SELECTOR": false,
                        "_DP_PEP_SI_ENABLE_VOICE_INPUT": false,
                        "_DP_PEP_SI_THREADS_API": false,
                        "_DP_PEP_SI_USE_AGENT_API_FOR_TITLE_GEN": false,
                        "_DP_PEP_SI_USE_AGENT_GATEWAY_ENDPOINT": false,
                        "_DP_PEP_SI_USE_DATA_AGENT": false,
                        "_DP_PEP_VERSION": "__DO_NOT_USE_SENTINEL_VALUE__",
                        "_DP_PEP_WAREHOUSE": false,
                        "_DP_PRIMITIVES_GRPC_PERCENTAGE": 100,
                        "_DP_PSGEX_AUTH_UI_MILESTONES_0_1_SNOWFLAKE_OAUTH": false,
                        "_DP_PSGEX_AUTH_UI_MILESTONE_2_EXTERNAL_OAUTH": false,
                        "_DP_PSGEX_AUTH_UI_MILESTONE_3_WIF": false,
                        "_DP_PSGEX_AUTH_UI_MILESTONE_4_OAUTH_WITH_WIF": false,
                        "_DP_SHARELESS_LISTINGS_GLOBAL_IDS": "",
                        "_DP_SHOW_WORKSPACES_ONBOARDING_FLOW": true,
                        "_DP_TEMPLATES_CMS_ENABLED": false,
                        "_DP_TEST1": "test1",
                        "_DP_TEST2": 2,
                        "_DP_TEST3": 5,
                        "_DP_TEST4": 500,
                        "_DP_TEST_PARAM_WORKSPACES_IFRAME_VERSION_CODE": "__DO_NOT_USE_SENTINEL_VALUE__",
                        "_DP_UIP_ENABLE_ACCOUNT_AGNOSTIC_DEEPLINK": true,
                        "_DP_UIP_REMEMBER_LAST_PAGE": false,
                        "_DP_UIP_REMEMBER_LAST_PAGE_USER_FLAG": false,
                        "_DP_UI_DBT_ACCOUNT_HISTORY_ROW_LIMIT": 50,
                        "_DP_UI_DBT_PROJECT_HISTORY_ROW_LIMIT": 100,
                        "_DP_UI_DISABLE_DBT_COMMAND_EXECUTION_DETAILS": false,
                        "_DP_UI_DISABLE_DBT_COMMAND_EXECUTION_DETAILS_REST_API": false,
                        "_DP_UI_DISABLE_RAW_MANAGE_APPROVAL": true,
                        "_DP_UI_DNA_GA_PREVIEW_PILL_REMOVED": true,
                        "_DP_UI_ENABLE_ACCOUNT_A_A_TESTING_2": false,
                        "_DP_UI_ENABLE_BUDGET_EMAIL_OPTIONAL": false,
                        "_DP_UI_ENABLE_CATALOG_DB_SELECTION_ON_SHARE_CREATION": true,
                        "_DP_UI_ENABLE_CHROMELESS_SIS_APPS": false,
                        "_DP_UI_ENABLE_CONSUMPTION_PAGE_PERFORMANCE_IMPROVEMENT_2": false,
                        "_DP_UI_ENABLE_CORTEX_DESCRIBE_TABLE_BUTTON": true,
                        "_DP_UI_ENABLE_CORTEX_GENERATE_TABLE_DESCRIPTION": true,
                        "_DP_UI_ENABLE_CORTEX_GOVERNOR_CHAT": false,
                        "_DP_UI_ENABLE_DATADOG_FLAG_EXPOSURE": true,
                        "_DP_UI_ENABLE_DATA_EXCHANGE_CHECK_FOR_DATA_PRODUCT_TREE": true,
                        "_DP_UI_ENABLE_DBT_IN_WORKSPACES": false,
                        "_DP_UI_ENABLE_DCM_IN_WORKSPACES": false,
                        "_DP_UI_ENABLE_EXPERIMENTATION_LOGGER": false,
                        "_DP_UI_ENABLE_FEATURE_PUBLIC_LISTINGS_IN_VPS": false,
                        "_DP_UI_ENABLE_FLAG_ACCURACY_CHECKS": true,
                        "_DP_UI_ENABLE_GEN2_WAREHOUSE_FEATURE": true,
                        "_DP_UI_ENABLE_HOME_TUTORIALS_ON_HOME_V2": false,
                        "_DP_UI_ENABLE_IMPROVED_CONSUMER_LISTING_CTA_BUTTON_LOADING": true,
                        "_DP_UI_ENABLE_IM_DATA_PRODUCT_TAB": true,
                        "_DP_UI_ENABLE_IM_HOMEPAGE_REFACTOR": true,
                        "_DP_UI_ENABLE_IM_PARTIAL_ATTRIBUTES_SAVE": true,
                        "_DP_UI_ENABLE_IM_PII_DATA_PRODUCTS_LISTING": false,
                        "_DP_UI_ENABLE_IM_RAW_DIALOG_FOR_DATA_PRODUCT_TREE": true,
                        "_DP_UI_ENABLE_INVOKE_LAUNCH_SERVICE_IN_SHADOW": false,
                        "_DP_UI_ENABLE_JOBS_HISTORY_UI": false,
                        "_DP_UI_ENABLE_LAF_REFERENCE_USAGE_UI": false,
                        "_DP_UI_ENABLE_LEARN_A_A_TESTING": true,
                        "_DP_UI_ENABLE_LINEAGE_EXTERNAL_LINEAGE": false,
                        "_DP_UI_ENABLE_LINEAGE_STORED_PROCEDURE": false,
                        "_DP_UI_ENABLE_LINEAGE_TASK": false,
                        "_DP_UI_ENABLE_LISTING_CTA_CORTEX_STUDIO_UPDATES": true,
                        "_DP_UI_ENABLE_LISTING_DATA_DICTIONARY_V2": false,
                        "_DP_UI_ENABLE_LISTING_TO_OFFER_MIGRATION": false,
                        "_DP_UI_ENABLE_MONITORING_QUERY_INSIGHTS_IN_WS": false,
                        "_DP_UI_ENABLE_MULTI_CATEGORY_SUPPORT": true,
                        "_DP_UI_ENABLE_NOTEBOOKS_CONTAINER_RUNTIME_SELECTION": false,
                        "_DP_UI_ENABLE_OBJECT_EXPLORER_SEMANTIC_VIEW_PHASE_2": false,
                        "_DP_UI_ENABLE_PARAMETER_LOADING_FLAKINESS_CHECK": false,
                        "_DP_UI_ENABLE_QUERY_HISTORY_DEFAULT_DATE_RANGE": true,
                        "_DP_UI_ENABLE_SHARED_TAGS_IN_COLUMN_LIST": true,
                        "_DP_UI_ENABLE_SHOW_IN_ACCOUNT_IN_SECURABLE_STORE": false,
                        "_DP_UI_ENABLE_SNOWSIGHT_WORKSHEETS_FEATURE_PROMO_SECTION": true,
                        "_DP_UI_ENABLE_STREAMLIT_ALWAYS_SHOW_IFRAME": true,
                        "_DP_UI_ENABLE_STREAMLIT_CACHE_BOOTSTRAP_DATA": false,
                        "_DP_UI_ENABLE_STREAMLIT_CACHE_IFRAME_URL": true,
                        "_DP_UI_ENABLE_STREAMLIT_DISABLE_RUN_BUTTON_ON_CLICK": false,
                        "_DP_UI_ENABLE_STREAMLIT_DISABLE_RUN_BUTTON_WHEN_APP_IS_NOT_INTERACTIVE": false,
                        "_DP_UI_ENABLE_STREAMLIT_DISABLE_RUN_BUTTON_WHILE_SCRIPT_IS_RUNNING": false,
                        "_DP_UI_ENABLE_STREAMLIT_EMIT_GUEST_READY_EVENT": false,
                        "_DP_UI_ENABLE_STREAMLIT_EMIT_LOAD_APP_EVENT_ON_INITIALIZATION_STATE_RUNNING": false,
                        "_DP_UI_ENABLE_STREAMLIT_IMPROVED_LOAD_SEQUENCE_FOR_NATIVE_APPS": true,
                        "_DP_UI_ENABLE_STREAMLIT_LIST_VIEW_RUNTIME_STATUS_COLUMN": false,
                        "_DP_UI_ENABLE_STREAMLIT_NEXT_CONTROLLER": false,
                        "_DP_UI_ENABLE_STREAMLIT_PROMPT_INPUT": false,
                        "_DP_UI_ENABLE_STREAMLIT_WEBSOCKET_HEALTHCHECK": false,
                        "_DP_UI_ENABLE_STREAMLIT_WEBSOCKET_HEALTHCHECK_SHADOW_MODE": true,
                        "_DP_UI_ENABLE_SUPPORT_AREA_TAXONOMY_FIX": true,
                        "_DP_UI_ENABLE_SUPPORT_ASK_CXE_FOR_AREA_TAXONOMY_SUGGESTION": true,
                        "_DP_UI_ENABLE_SUPPORT_CASE_CREATION_VALIDATES_ORGID": true,
                        "_DP_UI_ENABLE_SUPPORT_CONTROLLER_NAVIGATION": true,
                        "_DP_UI_ENABLE_SUPPORT_FEATURE_INLINE_SUGGESTIONS": true,
                        "_DP_UI_ENABLE_SUPPORT_FEATURE_INLINE_SUGGESTIONS_SERVER_SENT_EVENTS": false,
                        "_DP_UI_ENABLE_SUPPORT_FIX_CASE_CREATION_CONTACT_QUERY": false,
                        "_DP_UI_ENABLE_SUPPORT_MISSING_INFO_SUGGESTIONS": false,
                        "_DP_UI_ENABLE_TAGGING_IN_STANDARD_EDITION": true,
                        "_DP_UI_ENABLE_TAGS_ON_DB_SCHEMA_PAGE": false,
                        "_DP_UI_ENABLE_TIE_RESULT_TO_SQL_FILE": false,
                        "_DP_UI_ENABLE_TOP_TAGS_BY_SPEND_ORDERING_FIX": false,
                        "_DP_UI_ENABLE_TRUST_CENTER_ANOMALIES_TAB": false,
                        "_DP_UI_ENABLE_TRUST_CENTER_EXTENSIBILITY": false,
                        "_DP_UI_ENABLE_TRUST_CENTER_MFA_NOTIFICATION": false,
                        "_DP_UI_ENABLE_TRUST_CENTER_NOTIFICATION_SETTINGS": false,
                        "_DP_UI_ENABLE_TRUST_CENTER_ORG_VIEW": false,
                        "_DP_UI_ENABLE_TRUST_CENTER_WEEKLY_DIGEST_NOTIFICATION": false,
                        "_DP_UI_ENABLE_USER_A_A_TESTING": false,
                        "_DP_UI_ENABLE_USER_A_A_TESTING_2": true,
                        "_DP_UI_FEATURE_INFORMATICA_SUGGESTIONS_ENABLE": true,
                        "_DP_UI_FEATURE_INFORMATICA_VALIDATION_ENABLE": true,
                        "_DP_UI_FEATURE_MARKETPLACE_INVOICE_RETRY_PAYMENT_ENABLE": true,
                        "_DP_UI_FEATURE_ODDS_CONTACT_UPDATE_ENABLE": false,
                        "_DP_UI_FEATURE_ODDS_FEEDBACK_ENABLE": true,
                        "_DP_UI_FEATURE_OPENFLOW": false,
                        "_DP_UI_FEATURE_STRIPE_EVENTUALLY_DUE_ENABLED": false,
                        "_DP_UI_FEATURE_STRIPE_STABILITY_IMPROVEMENTS_ENABLED": false,
                        "_DP_UI_FIX_COPY_NAME_IN_WORKSHEETS": true,
                        "_DP_UI_FIX_DATE_FORMAT_SAFARI": true,
                        "_DP_UI_FIX_DB_ROLE_ACCESS": true,
                        "_DP_UI_FIX_GIT_SWITCHING": true,
                        "_DP_UI_FIX_IM_CONSUMER_LISTING_HEADER_ICON": true,
                        "_DP_UI_FIX_INFINITE_REDIRECT_INTERNAL_LISTING": true,
                        "_DP_UI_FIX_LAF_AUTOFULFILLMENT_REMOVE_LAST_CONSUMER": true,
                        "_DP_UI_FIX_LAF_SETTINGS_DIALOG_BUG": true,
                        "_DP_UI_FIX_LISTING_FILTER": true,
                        "_DP_UI_FIX_ROLE_IN_IM_WORKSHEETS_TREE": true,
                        "_DP_UI_FIX_RPF_INCLUDE_UNKOWN_MANIFEST_UPDATES": true,
                        "_DP_UI_FIX_ULL_COPY": true,
                        "_DP_UI_GOVERNANCE_COPILOT_MESSAGE_HISTORY_COUNT": 6,
                        "_DP_UI_GOVERNANCE_SHOW_LOADING_INDICATOR": false,
                        "_DP_UI_IM_FOLLOW_UP_FIXES": true,
                        "_DP_UI_INTEGRATIONS_CREATE_NOTIFICATION_INTEGRATION": false,
                        "_DP_UI_INTEGRATIONS_CREATE_STORAGE_INTEGRATION": false,
                        "_DP_UI_INTEGRATIONS_INTEGRATION_DETAILS_POPOVER": false,
                        "_DP_UI_INTEGRATIONS_INTEGRATION_SELECTOR": false,
                        "_DP_UI_INTEGRATIONS_THIRD_PARTY_ICONS": false,
                        "_DP_UI_INTEGRATIONS_VALIDATE_CATALOG_INTEGRATION": false,
                        "_DP_UI_INTEGRATIONS_VALIDATE_NOTIFICATION_INTEGRATION": false,
                        "_DP_UI_INTEGRATIONS_VALIDATE_STORAGE_INTEGRATION": false,
                        "_DP_UI_LISTING_COPILOT_REFACTOR": true,
                        "_DP_UI_NOTEBOOK_ACCEPT_FILE_UPLOAD_EXTENSIONS": "",
                        "_DP_UI_NOTEBOOK_CELL_RESULT_GRAPH_GENERATION_PROMPT": "Analyze the SQL in {cellName} and generate complete, runnable Streamlit python codes that creates appropriate charts/graphs based on the query results.\n\nYou can get the cell's SQL result data with:\n```python\ndf = {cellName}.to_pandas()\n```\n\nOnly Return the Python codes.\n",
                        "_DP_UI_NOTEBOOK_DETAIL_PAGE_PROVIDER": false,
                        "_DP_UI_NOTEBOOK_DISABLE_WAREHOUSE_OPTION": false,
                        "_DP_UI_NOTEBOOK_ENABLE_EXPAND_RESULTS_INLINE_CELL_BUTTON": false,
                        "_DP_UI_NOTEBOOK_ENABLE_NOTEBOOK_GRAPH_AUTO_GENERATION": false,
                        "_DP_UI_NOTEBOOK_ENABLE_PDB_SCHEDULING": false,
                        "_DP_UI_NOTEBOOK_ENABLE_RESOURCE_USAGE_DISK_STATS": false,
                        "_DP_UI_NOTEBOOK_ENABLE_RESOURCE_USAGE_PANE": false,
                        "_DP_UI_NOTEBOOK_ENABLE_UNIFIED_DATAGRID": false,
                        "_DP_UI_NOTEBOOK_GET_STREAMLIT_VERSION_FOR_WH_ACTIVE_INSTANCE": true,
                        "_DP_UI_NOTEBOOK_LOCAL_DEVELOPMENT_STREAMLIT_LIB_VERSION": "1.39",
                        "_DP_UI_NOTEBOOK_PACKAGE_UPDATE_ALERT": true,
                        "_DP_UI_NOTEBOOK_READ_CLIENT_MANIFEST": false,
                        "_DP_UI_NOTEBOOK_RESULTS_ENHANCEMENTS": true,
                        "_DP_UI_NOTEBOOK_RUNTIME_EXCLUSION_LIST": "stable,preview",
                        "_DP_UI_NOTEBOOK_SHOW_STREAMLIT_UPGRADE_WARNING_BANNER": "none",
                        "_DP_UI_NOTEBOOK_SKIP_WEBSOCKET_PING": false,
                        "_DP_UI_NOTEBOOK_SKIP_WEBSOCKET_PING_AB_TEST": true,
                        "_DP_UI_NOTEBOOK_TOTAL_BLOCKING_TIME": true,
                        "_DP_UI_NOTEBOOK_UNWIND_SHARING_DPO_SAVE": false,
                        "_DP_UI_OPENFLOW_CHECK_LEGAL_PAPER_TOS_REQUIRED_ORG_FLAG": true,
                        "_DP_UI_OPENFLOW_DISABLE_PRIVATE_LINK_REDIRECT": false,
                        "_DP_UI_OPENFLOW_TOS_ACCEPTANCE_OPTIONAL": true,
                        "_DP_UI_OPENFLOW_USE_ACCOUNT_ID_FOR_REDIRECT": false,
                        "_DP_UI_POLARIS_CATALOG_FEDERATION": false,
                        "_DP_UI_POLARIS_DEVTOOLS": false,
                        "_DP_UI_POLARIS_ENABLE_GRANT_SNOWFLAKE_ROLES": true,
                        "_DP_UI_POLARIS_ENABLE_OUTBOUND_PRIVATE_LINK": false,
                        "_DP_UI_POLARIS_FIX_PRIVATE_LINK": true,
                        "_DP_UI_POLARIS_ORPHAN_FILE_REMOVAL_POLICY": false,
                        "_DP_UI_POLARIS_PRIVATE_LINK": true,
                        "_DP_UI_POLARIS_SHOW_DELETE_POLICY": false,
                        "_DP_UI_POLARIS_SIGV4_AUTH": false,
                        "_DP_UI_POLARIS_TABLE_MAINTENANCE": false,
                        "_DP_UI_POLARIS_TABLE_OPTIMIZATION": false,
                        "_DP_UI_POLARIS_VALIDATE_CONFIGURATION": true,
                        "_DP_UI_PROVIDER_ENABLE_CRON_UPDATE_IN_LAF": true,
                        "_DP_UI_PROVIDER_ENABLE_NON_SECURE_SHARING": true,
                        "_DP_UI_PROVIDER_ENABLE_RESHARING_INTERNAL_LISTING_PRPR": false,
                        "_DP_UI_RAW_PERFORMANCE_IMPROVEMENTS": true,
                        "_DP_UI_REMOVE_DEFAULT_REGION_FILTER_FROM_BROWSE": true,
                        "_DP_UI_SEARCH_ENABLE_OBJECT_PREVIEW_UPDATES": false,
                        "_DP_UI_SHOULD_SHOW_MFE_DEBUG_TOOLING_MENU_ITEM": false,
                        "_DP_UI_SHOULD_SHOW_MFE_TESTBED_MENU_ITEM": false,
                        "_DP_UI_SKIP_MODEL_VALIDATION": true,
                        "_DP_UI_SKIP_POWERLESS_BUTTONS_PERSONALIZED_LISTING": true,
                        "_DP_UI_TARGET_EXTERNAL_ORGANIZATIONS_GA_ENABLED": false,
                        "_DP_UI_USE_ALTER_SHARE_REMOVE_ACCOUNTS": true,
                        "_DP_UI_USE_UPDATE_MONETIZATION_OBJECTS_ENDPOINT": true,
                        "_DP_WORKSPACES_AGENTIC_CHART_MODEL": "claude-3-7-sonnet",
                        "_DP_WORKSPACES_ASSISTANT_MODEL_TO_USE": "claude-3-5-sonnet",
                        "_DP_WORKSPACES_CACHE_QUERY_CLIENT": true,
                        "_DP_WORKSPACES_DISABLE_INLINE_COPILOT": false,
                        "_DP_WORKSPACES_ENABLE_AGENTIC_CHART": false,
                        "_DP_WORKSPACES_ENABLE_BALTO_BUTTONS_THEME": true,
                        "_DP_WORKSPACES_ENABLE_BUTTON_THEME": false,
                        "_DP_WORKSPACES_ENABLE_CHART_API": false,
                        "_DP_WORKSPACES_ENABLE_CHART_HOVER_LAYER": false,
                        "_DP_WORKSPACES_ENABLE_DATE_RANGE_FILTER": false,
                        "_DP_WORKSPACES_ENABLE_EMERGENCY_ERROR_PAGE": false,
                        "_DP_WORKSPACES_ENABLE_FILE_DOWNLOAD": true,
                        "_DP_WORKSPACES_ENABLE_IMPORT_TO_WORKSPACE": false,
                        "_DP_WORKSPACES_ENABLE_MIGRATION_OPT_IN": false,
                        "_DP_WORKSPACES_ENABLE_MULTI_MODAL_DATA_TYPES": false,
                        "_DP_WORKSPACES_ENABLE_OMNI_SEARCH": false,
                        "_DP_WORKSPACES_ENABLE_PROJECT_FILES_DRAFT": false,
                        "_DP_WORKSPACES_ENABLE_PROJECT_FILES_QUERY": false,
                        "_DP_WORKSPACES_ENABLE_SAVE_CHART": false,
                        "_DP_WORKSPACES_ENABLE_WORKSPACE_SHARING": false,
                        "_DP_WORKSPACES_FIX_LOADER_THROW_ON_REFETCH": true,
                        "_DP_WORKSPACES_GIT": false,
                        "_DP_WORKSPACES_GIT_CHANGES_API_THROTTLE_TIMEOUT_IN_SECONDS": 5,
                        "_DP_WORKSPACES_GIT_UX_FLOWS": true,
                        "_DP_WORKSPACES_INTERNAL_QUERIES_ALL_SECONDARY_ROLES": "unset",
                        "_DP_WORKSPACES_MIGRATE_WORKSHEET_USERS_TO_WORKSPACES": false,
                        "_DP_WORKSPACES_NEW_NOTEBOOK_EDITOR": false,
                        "_DP_WORKSPACES_NOTEBOOKS_EDITOR": false,
                        "_DP_WORKSPACES_NOTEBOOKS_EDITOR_EXECUTE": false,
                        "_DP_WORKSPACES_NOTEBOOK_PACKAGE_PICKER_UI": false,
                        "_DP_WORKSPACES_PERSIST_QUERY_CLIENT": true,
                        "_DP_WORKSPACES_PROMO_IN_V1_QUERY_STATUS": false,
                        "_DP_WORKSPACES_RELEASE_CHANNEL": "stable",
                        "_DP_WORKSPACES_SHARE_METADATA": false,
                        "_DP_WORKSPACES_SHOW_CHART_EVAL_TOOLS": false,
                        "_DP_WORKSPACES_SHOW_CHART_IN_SELECTION_STATS": false,
                        "_DP_WORKSPACES_SHOW_CHART_SUGGESTIONS": false,
                        "_DP_WORKSPACES_SHOW_DEV_TOOLS": false,
                        "_DP_WORKSPACES_USE_WORKSPACE_FBE": false
                    },
                    "virtualFolderSorts": {},
                    "connections": {},
                    "numWorksheetsInClassicUI": 0,
                    "defaultRole": "ACCOUNTADMIN",
                    "defaultWarehouse": "COMPUTE_WH",
                    "defaultDatabase": "",
                    "defaultSchema": "",
                    "defaultSecondaryRoles": "ALL",
                    "customLandingPageUrl": "",
                    "customLandingPageUrlFlushNextUiLoad": false,
                    "acceptedTerms": {},
                    "pinnedItems": "",
                    "streamlitSidebarCollapsed": false,
                    "streamlitEditorCollapsed": false,
                    "streamlitAppCollapsed": false,
                    "worksheetsSidebarCollapsed": false,
                    "isNewUser": true
                },
                "username": "HEWSEEP",
                "availableRoles": [
                    "ACCOUNTADMIN",
                    "ORGADMIN",
                    "PUBLIC",
                    "SECURITYADMIN",
                    "SNOWFLAKE_LEARNING_ROLE",
                    "SYSADMIN",
                    "USERADMIN"
                ],
                "exchangeRoles": [],
                "hasAdminRole": true,
                "hasAccountAdminRole": true,
                "gsCreatedOn": *************
            }
        }
    },
    "pid": "3tG8cGGSvgs"
}

