#!/usr/bin/env python3
# -*- coding: utf-8 -*-

with open('snowflake_register.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

new_lines = []
for line in lines:
    if 'range(60)' in line and '5分钟' in line:
        new_line = line.replace('range(60)', 'range(120)').replace('5分钟', '10分钟')
        new_lines.append(new_line)
    elif '/60)' in line and '验证码解决中' in line:
        new_line = line.replace('/60)', '/120)')
        new_lines.append(new_line)
    elif '# 如果 YesCaptcha 失败，使用备用 token' in line:
        new_lines.append('        # YesCaptcha 失败时返回 None\n')
        new_lines.append('        return None\n')
        break  # 停止处理后续行
    else:
        new_lines.append(line)

with open('snowflake_register.py', 'w', encoding='utf-8') as f:
    f.writelines(new_lines)

print('修改完成')
