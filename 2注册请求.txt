验证captcha之后

curl 'https://signup.snowflake.com/api/v1/createtrial' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -b 'signupsession=eyJleHBlcmltZW50U2VnbWVudHMiOnsiU0FOSVRZX0FBX1RFU1QiOjF9fQ==; signupsession.sig=QvGyu2U7LKU5ePq1KDZdVYsttXE' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'signup-flow-type: Default' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-mutiny-experience-count: 0' \
  --data-raw '{"firstName":"rwe","lastName":"rwqe","email":"<EMAIL>","company":"hewseep","role":"ceo","edition":"Enterprise","cloud":"aws","region":"ap-northeast-1","country":"Hong Kong","recaptchaToken":"03AFcWeA4OCNEPkvtATYf3sybaVKs0Ov-073ftksLy1PXZs_yK8LAoOZ59h9R25pdApiKQHy_YEeUhCIBMjTGyJKemQzKi0Nk2pB5wKVlWfhIxL9uxd-F9B5AU0eaTd6jJGiqVMiiNv1RkcGd0mAGC-dGU7Ozat8iugWZ65hnAeRP_Y9OnvR6rqCQ-FCyuBKfwiaeiltO1FkFUI0EpkDld-MUtbkDNHr98ERQqZiz6G_fyFrmSMZFgvZRVnXhrMJqiDGBvwZlNzaJn2cQ3c0FEtu_POWlCefWO7lI_v4YR0RD9ltXkTntIcK9zof9xq_clNkVaIUrshT9U5m_jJ34qA_Mwzadk323uOG_eAnfbPYaSpgoT7O7wwsuCVGGLOjGXPuUw5Hk1CB4r01iDyxcZoEkMop4U2d8zfAjrglNfuj07StyfOZQ8_EodMhSLHftV9F6v8ex4gdbzsFfzXcWwX5ZFTTaXvDjm-KMTHTs39_dCNecA9WEC0qHfXAR0vgL14CIxxknIXHvYo1az2WJKcSzsIWAw929ml_nH43NQZEEL-fNr1F1R-LCIILDWyJ-JYG2OHhJDVFtmk4cvXlWyIW6UghkX1uQBIVoWVQ0Lft091mOD1J1fv1eZhvIfWTV73N7VwhbnDn50tvETeinBAHDdkg1Z7Lv6dTfxJY2nXfBLm3S96B6fUZjEHislPgayQD1jTqfij4w-m3GEmKn4r-uMec3aYp60DMgnjocvi3nqH_BWhOg03mfzjAICcCah3ov7OuBOSVOjTe3JnMGhexxbntFhKiAxd5JlfVhI1RMFmKAG1SfUrE6SKbZJKzCYR1HWAzFu1HuXi0cIqF-YELknjyOo_l_MN26A9C5nK3rHbG8VP5HRna72BHxhbMRgcYjJ622LFbnDXo6W62hU5KvJoAwxNqdlRoGj2B1ZPEExY-MVrFYlWIkQTCEEBG0eJPbipCnOZtyt","signupUrl":"https://signup.snowflake.com/","formId":"e60f82bd-c49c-42e5-a3a6-fe00a163e4f4","formTwoCompletionTime":10.945}'

{"success":true,"data":{"locator":"OG38984"}}

curl 'https://signup.snowflake.com/api/v1/telemetry_beacon' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -b 'signupsession=eyJleHBlcmltZW50U2VnbWVudHMiOnsiU0FOSVRZX0FBX1RFU1QiOjF9fQ==; signupsession.sig=QvGyu2U7LKU5ePq1KDZdVYsttXE' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=4, i' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-origin' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  --data-raw '{"body":[{"component":"SignupPage","location":["SignupPage"],"interaction":true,"flags":{},"event":"signup_form_rendering","type":"ui_response_success","data":{"signupFlowType":"Default","clientVersion":"119-0e8dec7","isoCode":"HK","coordiates":[22.28000069,114.15000153],"locale":"en-US","docsLocale":"en","experimentGroups":{"SANITY_AA_TEST":"FIRST"},"featureFlags":{"FF_ENABLE_LOAD_LISTINGS":false,"FF_ENABLE_VA2_BLOCK":false,"FF_ENABLE_CUSTOM_LANDING_PAGE_URL":true,"FF_ENABLE_AWS_MUMBAI_BLOCK":true,"FF_HIDE_US_EAST_1_REGION":false,"FF_ENABLE_DEFAULT_WORKSHEETS":true,"FF_ENABLE_PRELOAD_TASTY_BYTES":false,"FF_ENABLE_SETUP_SANDBOX_ENV":true,"FF_ENABLE_SFDC_SKIP":false,"FF_ENABLE_GROUPED_REGIONS":false,"FF_ENABLE_JAPAN_PHONE_NUMBER":true,"FF_ENABLE_REQUEST_V2_API":false,"FF_ENABLE_SALESFORCE_UPDATE_RECORDING":false,"FF_ENABLE_MUTINY_EXPERIENCE_TRACKING":true},"referralUrl":"","formId":"e60f82bd-c49c-42e5-a3a6-fe00a163e4f4","timestamp":"2025-08-01T06:06:56.218Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupPage","location":["SignupPage"],"interaction":false,"flags":{},"event":"signup_compute_default_region","type":"ui_response_success","data":{"aws":"ap-northeast-2","azure":"koreacentral","gcp":"me-central2","formId":"e60f82bd-c49c-42e5-a3a6-fe00a163e4f4","timestamp":"2025-08-01T06:07:03.599Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupCard","location":["SignupPage"],"interaction":true,"flags":{},"event":"signup_form_one_complete","type":"ui_click","data":{"firstName":"rwe","lastName":"rwqe","email":"<EMAIL>","country":"Hong Kong","optOutEmailAgreement":false,"formId":"e60f82bd-c49c-42e5-a3a6-fe00a163e4f4","signupReason":"Other","formOneCompletionTime":6.059999942779541,"timestamp":"2025-08-01T06:07:05.473Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/","mutinyExperienceCount":0,"mutinyExperiences":[]}},{"component":"SignupCard","location":["SignupPage"],"interaction":true,"flags":{},"event":"signup_form_two_complete","type":"ui_click","data":{"firstName":"rwe","lastName":"rwqe","email":"<EMAIL>","company":"hewseep","role":"ceo","edition":"Enterprise","cloud":"aws","region":"ap-northeast-1","country":"Hong Kong","recaptchaToken":"REDACTED","signupUrl":"https://signup.snowflake.com/","formId":"e60f82bd-c49c-42e5-a3a6-fe00a163e4f4","formTwoCompletionTime":10.945,"referrerUrl":"","timestamp":"2025-08-01T06:07:21.786Z","parsedParams":{},"fullUrl":"https://signup.snowflake.com/","mutinyExperienceCount":0,"mutinyExperiences":[]}}],"headers":{"signup-flow-type":"Default","x-mutiny-experience-count":"0"}}'

{"success":true}


两个不知道是什么的

curl 'https://kimi.moonshot.cn/api/ext/annotation/list' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: authorization,content-type,r-timezone,x-msh-platform,x-msh-version' \
  -H 'access-control-request-method: POST' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'

curl 'https://kimi.moonshot.cn/api/ext/annotation/list' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'authorization: Bearer eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ1c2VyLWNlbnRlciIsImV4cCI6MTc1NjYyMDM1MCwiaWF0IjoxNzU0MDI4MzUwLCJqdGkiOiJkMjY1aWZtZjJrcWNqZjI2MGFpMCIsInR5cCI6ImFjY2VzcyIsImFwcF9pZCI6ImtpbWkiLCJzdWIiOiJjcGRwMXF0dmJmNnRwNGpzZjViMCIsInNwYWNlX2lkIjoiY3BkcDFxdHZiZjZ0cDRqc2Y1YWciLCJhYnN0cmFjdF91c2VyX2lkIjoiY3BkcDFxdHZiZjZ0cDRqc2Y1YTAiLCJyb2xlcyI6WyJ2aWRlb19nZW5fYWNjZXNzIiwiZGVlcF9yZXNlYXJjaCJdLCJzc2lkIjoiMTczMDMxMjQ0Mzc2NTY3ODY4MSIsInJlZ2lvbiI6ImNuIn0.HedXD8jHvMeKBU-ZcXCdxnntVwAfsIxH3TR3wut-ZvGC0vbp4froJQ70okKQBE3HglsfRb-hnOc5O2qRF9fxaA' \
  -H 'content-type: application/json' \
  -H 'origin: https://signup.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'r-timezone: Asia/Shanghai' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-msh-platform: web-extension' \
  -H 'x-msh-version: 1.1.3' \
  --data-raw '{"url":"https://signup.snowflake.com/#"}'

