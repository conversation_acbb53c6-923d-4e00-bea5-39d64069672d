curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/queries?desc=add-programmatic-access-token' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: content-type,traceparent,x-numeracy-client-version,x-snowflake-context,x-snowflake-context-encoded,x-snowflake-request-id' \
  -H 'access-control-request-method: POST' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'

curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/queries?desc=add-programmatic-access-token' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-0000000000000000c89d86c7eac7283f-73eae4613c5bc501-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-request-id: b8c6b3df-3d35-4e82-a417-f7166429babd' \
  --data-raw '{"sqlText":"ALTER USER ADD PROGRAMMATIC ACCESS TOKEN \"*********\" DAYS_TO_EXPIRY = 30","internal":true,"executionContext":{"role":"ACCOUNTADMIN"},"bindingValues":{},"maxResults":100,"deadlineInMs":12000}'


{
    "queryId": "01be1294-0002-a6ee-0000-0002a656a019",
    "status": {
        "summary": "SUCCESS",
        "statusDescription": "SUCCESS",
        "errorCode": 0,
        "errorMessage": null,
        "startTime": *************,
        "endTime": *************,
        "selectedFragmentCount": 0,
        "sqlText": "ALTER USER ADD PROGRAMMATIC ACCESS TOKEN \"*********\" DAYS_TO_EXPIRY = 30",
        "state": "SUCCEEDED",
        "statesDuration": "[40,0,49,0,0,0,0,0,0,0,0,0,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]",
        "stats": {
            "compilationTime": 40,
            "gsExecTime": 49,
            "xpExecTime": null,
            "ioLocalFdnReadBytes": null,
            "ioLocalFdnWriteBytes": null,
            "ioRemoteFdnReadBytes": null,
            "ioRemoteFdnWriteBytes": null,
            "localDop": null,
            "queuedResumeTime": null,
            "prepareLoadTime": null,
            "producedRows": null,
            "scanAssignedBytes": null,
            "scanAssignedFiles": null,
            "scanBytes": null,
            "scanFiles": null,
            "scanOriginalFiles": null,
            "scheduleTime": null,
            "warehouseSize": null
        },
        "totalDuration": 107,
        "warehouseExternalSize": null,
        "warehouseName": "COMPUTE_WH",
        "estimatedMinimumRuntimeMillis": null,
        "requestId": "b8c6b3df-3d35-4e82-a417-f7166429babd",
        "queryTag": "",
        "queryClientTag": null,
        "numOfQueryInsights": 0
    },
    "result": {
        "finalDatabaseName": null,
        "finalSchemaName": null,
        "finalRoleName": "ACCOUNTADMIN",
        "finalWarehouseName": "COMPUTE_WH",
        "statementType": "DDL",
        "totalRowCountTruncated": false,
        "uncompressedDataSizeInBytes": 231,
        "downloadDisabledDueToSize": false,
        "parameters": {
            "CLIENT_PREFETCH_THREADS": 4,
            "TIMESTAMP_OUTPUT_FORMAT": "YYYY-MM-DD HH24:MI:SS.FF3 TZHTZM",
            "TIME_OUTPUT_FORMAT": "HH24:MI:SS",
            "TIMESTAMP_TZ_OUTPUT_FORMAT": "",
            "CLIENT_RESULT_CHUNK_SIZE": 160,
            "CLIENT_SESSION_KEEP_ALIVE": false,
            "QUERY_CONTEXT_CACHE_SIZE": 5,
            "CLIENT_METADATA_USE_SESSION_DATABASE": false,
            "CLIENT_OUT_OF_BAND_TELEMETRY_ENABLED": false,
            "ENABLE_STAGE_S3_PRIVATELINK_FOR_US_EAST_1": false,
            "TIMESTAMP_NTZ_OUTPUT_FORMAT": "YYYY-MM-DD HH24:MI:SS.FF3",
            "CLIENT_RESULT_PREFETCH_THREADS": 1,
            "CLIENT_METADATA_REQUEST_USE_CONNECTION_CTX": false,
            "CLIENT_HONOR_CLIENT_TZ_FOR_TIMESTAMP_NTZ": true,
            "CLIENT_MEMORY_LIMIT": 1536,
            "CLIENT_TIMESTAMP_TYPE_MAPPING": "TIMESTAMP_LTZ",
            "TIMEZONE": "America/Los_Angeles",
            "CLIENT_RESULT_PREFETCH_SLOTS": 2,
            "CLIENT_TELEMETRY_ENABLED": true,
            "CLIENT_USE_V1_QUERY_API": true,
            "CLIENT_DISABLE_INCIDENTS": true,
            "CLIENT_RESULT_COLUMN_CASE_INSENSITIVE": false,
            "CSV_TIMESTAMP_FORMAT": "",
            "BINARY_OUTPUT_FORMAT": "HEX",
            "CLIENT_ENABLE_LOG_INFO_STATEMENT_PARAMETERS": false,
            "CLIENT_TELEMETRY_SESSIONLESS_ENABLED": true,
            "DATE_OUTPUT_FORMAT": "YYYY-MM-DD",
            "CLIENT_STAGE_ARRAY_BINDING_THRESHOLD": 65280,
            "CLIENT_SESSION_KEEP_ALIVE_HEARTBEAT_FREQUENCY": 3600,
            "CLIENT_SESSION_CLONE": false,
            "AUTOCOMMIT": true,
            "TIMESTAMP_LTZ_OUTPUT_FORMAT": ""
        },
        "columnCount": 2,
        "resultColumnMetadata": [
            {
                "name": "token_name",
                "typeName": "VARCHAR",
                "type": 12,
                "nullable": true,
                "length": 16777216,
                "precision": 0,
                "scale": 0,
                "fixed": false,
                "base": "TEXT",
                "fields": [],
                "columnSrcTable": "",
                "columnSrcSchema": "",
                "columnSrcDatabase": "",
                "dimension": 0,
                "autoIncrement": false
            },
            {
                "name": "token_secret",
                "typeName": "VARCHAR",
                "type": 12,
                "nullable": true,
                "length": 16777216,
                "precision": 0,
                "scale": 0,
                "fixed": false,
                "base": "TEXT",
                "fields": [],
                "columnSrcTable": "",
                "columnSrcSchema": "",
                "columnSrcDatabase": "",
                "dimension": 0,
                "autoIncrement": false
            }
        ],
        "resultVersion": 1,
        "chunkFileCount": 1,
        "chunkFileMetadatas": [
            {
                "rowCount": 1,
                "uncompressedByteSize": 231
            }
        ],
        "mostRecentTransform": null,
        "displayOptions": null,
        "firstChunkData": "[[\"*********\",\"*********************************************************************************************************************************************************************************************************************\"]]",
        "firstChunkRowCount": 1,
        "columnStatsList": null,
        "columnStatsComputeDurationMs": 0
    }
}


其中的*********************************************************************************************************************************************************************************************************************
就是密钥