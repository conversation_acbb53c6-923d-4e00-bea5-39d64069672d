#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更稳定的 YesCaptcha 验证码解决方法
"""

import requests
import time

def solve_recaptcha_robust():
    """稳定的 YesCaptcha 解决 reCAPTCHA v2 Enterprise"""
    print("🔄 使用 YesCaptcha 解决 reCAPTCHA v2 Enterprise...")

    # YesCaptcha API 配置
    api_key = "206ea668e69a93830e0b5e9cfa2f9661bb5735da75102"
    site_key = "6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV"
    page_url = "https://signup.snowflake.com/"

    # 配置 session 以提高稳定性
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })

    try:
        # 构建任务数据
        task_data = {
            "clientKey": api_key,
            "task": {
                "type": "RecaptchaV2EnterpriseTaskProxyless",
                "websiteURL": page_url,
                "websiteKey": site_key,
                "enterprisePayload": {
                    "s": "SIGNUP"
                }
            }
        }

        print("⏳ 提交验证码任务...")
        
        # 提交任务，增加重试机制
        task_id = None
        for submit_attempt in range(3):
            try:
                response = session.post(
                    "https://api.yescaptcha.com/createTask",
                    json=task_data,
                    timeout=30
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("errorId") == 0:
                        task_id = result["taskId"]
                        print(f"✅ 任务已提交，ID: {task_id}")
                        break
                    else:
                        print(f"❌ 提交任务失败: {result.get('errorDescription', 'Unknown error')}")
                else:
                    print(f"❌ 提交任务失败: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"⚠️ 提交任务异常 (尝试 {submit_attempt + 1}/3): {e}")
                if submit_attempt < 2:
                    time.sleep(5)
                    continue

        if not task_id:
            print("❌ 无法提交验证码任务")
            return None

        # 等待解决结果 - 给更多时间，减少超时
        print("⏳ 等待验证码解决...")
        for attempt in range(120):  # 最多等待10分钟
            time.sleep(5)

            # 获取结果，增加重试机制
            for get_attempt in range(3):
                try:
                    result_response = session.post(
                        "https://api.yescaptcha.com/getTaskResult",
                        json={
                            "clientKey": api_key,
                            "taskId": task_id
                        },
                        timeout=30  # 减少超时时间
                    )

                    if result_response.status_code == 200:
                        result = result_response.json()
                        
                        if result.get("status") == "ready":
                            token = result["solution"]["gRecaptchaResponse"]
                            print(f"✅ reCAPTCHA 解决成功: {token[:50]}...")
                            return token
                        elif result.get("status") == "processing":
                            print(f"⏳ 验证码解决中... ({attempt + 1}/120)")
                            break  # 跳出重试循环，继续等待
                        else:
                            error_msg = result.get("errorDescription", "Unknown error")
                            print(f"❌ 验证码解决失败: {error_msg}")
                            return None
                    else:
                        print(f"⚠️ 获取结果失败: HTTP {result_response.status_code}")
                        
                except Exception as e:
                    print(f"⚠️ 获取结果异常 (尝试 {get_attempt + 1}/3): {e}")
                    if get_attempt < 2:
                        time.sleep(2)
                        continue
                    else:
                        print(f"⚠️ 第 {attempt + 1} 次查询失败，继续等待...")
                        break

    except Exception as e:
        print(f"❌ YesCaptcha 解决失败: {e}")

    # 失败时返回 None
    print("❌ 验证码解决失败，无法继续注册")
    return None

if __name__ == "__main__":
    token = solve_recaptcha_robust()
    if token:
        print(f"成功获取 token: {token[:50]}...")
    else:
        print("获取 token 失败")
