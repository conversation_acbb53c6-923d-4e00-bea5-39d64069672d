    def solve_recaptcha(self):
        """使用 YesCaptcha 解决 reCAPTCHA v2 Enterprise"""
        print("🔄 使用 YesCaptcha 解决 reCAPTCHA v2 Enterprise...")

        # YesCaptcha API 配置
        api_key = "206ea668e69a93830e0b5e9cfa2f9661bb5735da75102"
        site_key = "6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV"
        page_url = "https://signup.snowflake.com/"

        try:
            # 构建任务数据
            task_data = {
                "clientKey": api_key,
                "task": {
                    "type": "RecaptchaV2EnterpriseTaskProxyless",
                    "websiteURL": page_url,
                    "websiteKey": site_key,
                    "enterprisePayload": {
                        "s": "SIGNUP"
                    }
                }
            }

            print("⏳ 提交验证码任务...")
            response = requests.post(
                "https://api.yescaptcha.com/createTask",
                json=task_data,
                timeout=60
            )

            if response.status_code != 200:
                print(f"❌ 提交任务失败: HTTP {response.status_code}")
                return None

            result = response.json()
            if result.get("errorId") != 0:
                print(f"❌ 提交任务失败: {result.get('errorDescription', 'Unknown error')}")
                return None

            task_id = result["taskId"]
            print(f"✅ 任务已提交，ID: {task_id}")

            # 等待解决结果
            print("⏳ 等待验证码解决...")
            for attempt in range(120):  # 最多等待10分钟
                time.sleep(5)

                result_response = requests.post(
                    "https://api.yescaptcha.com/getTaskResult",
                    json={
                        "clientKey": api_key,
                        "taskId": task_id
                    },
                    timeout=60
                )

                if result_response.status_code != 200:
                    print(f"⚠️ 获取结果失败: HTTP {result_response.status_code}")
                    continue

                result = result_response.json()

                if result.get("status") == "ready":
                    token = result["solution"]["gRecaptchaResponse"]
                    print(f"✅ reCAPTCHA 解决成功: {token[:50]}...")
                    return token
                elif result.get("status") == "processing":
                    print(f"⏳ 验证码解决中... ({attempt + 1}/120)")
                    continue
                else:
                    error_msg = result.get("errorDescription", "Unknown error")
                    print(f"❌ 验证码解决失败: {error_msg}")
                    break

        except Exception as e:
            print(f"❌ YesCaptcha 解决失败: {e}")

        # YesCaptcha 失败时返回 None
        return None
