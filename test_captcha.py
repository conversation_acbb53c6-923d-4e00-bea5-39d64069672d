#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 YesCaptcha 验证码服务
"""

import requests
import time
import json

def test_yescaptcha():
    """测试 YesCaptcha 服务"""
    print("🔧 测试 YesCaptcha 服务")
    print("=" * 50)
    
    # YesCaptcha API 配置
    api_key = "206ea668e69a93830e0b5e9cfa2f9661bb5735da75102"
    site_key = "6LeYJsQZAAAAAEbi9tKooYTUzJjjwTvbCR0j_1lV"
    page_url = "https://signup.snowflake.com/"
    
    try:
        # 1. 测试 API 连接
        print("📡 测试 API 连接...")
        test_response = requests.get("https://api.yescaptcha.com", timeout=10)
        print(f"✅ API 连接成功: {test_response.status_code}")
        
        # 2. 提交验证码任务
        print("\n🔄 提交 reCAPTCHA v2 Enterprise 任务...")
        task_data = {
            "clientKey": api_key,
            "task": {
                "type": "RecaptchaV2EnterpriseTaskProxyless",
                "websiteURL": page_url,
                "websiteKey": site_key,
                "enterprisePayload": {
                    "s": "SIGNUP"
                }
            }
        }
        
        response = requests.post(
            "https://api.yescaptcha.com/createTask",
            json=task_data,
            timeout=60
        )
        
        print(f"📤 提交响应状态: {response.status_code}")
        print(f"📤 提交响应内容: {response.text}")
        
        if response.status_code != 200:
            print("❌ 提交任务失败")
            return False
            
        result = response.json()
        if result.get("errorId") != 0:
            print(f"❌ 提交任务失败: {result.get('errorDescription', 'Unknown error')}")
            return False
            
        task_id = result["taskId"]
        print(f"✅ 任务已提交，ID: {task_id}")
        
        # 3. 等待解决结果
        print("\n⏳ 等待验证码解决...")
        for attempt in range(30):  # 最多等待2.5分钟
            time.sleep(5)
            
            result_response = requests.post(
                "https://api.yescaptcha.com/getTaskResult",
                json={
                    "clientKey": api_key,
                    "taskId": task_id
                },
                timeout=60
            )
            
            print(f"📥 查询响应状态: {result_response.status_code}")
            
            if result_response.status_code != 200:
                print(f"⚠️ 获取结果失败: HTTP {result_response.status_code}")
                continue
                
            result = result_response.json()
            print(f"📥 查询响应内容: {result}")
            
            if result.get("status") == "ready":
                token = result["solution"]["gRecaptchaResponse"]
                print(f"\n🎉 验证码解决成功!")
                print(f"🔑 Token: {token[:50]}...")
                return token
            elif result.get("status") == "processing":
                print(f"⏳ 验证码解决中... ({attempt + 1}/30)")
                continue
            else:
                error_msg = result.get("errorDescription", "Unknown error")
                print(f"❌ 验证码解决失败: {error_msg}")
                break
                
        print("❌ 验证码解决超时")
        return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_balance():
    """测试账户余额"""
    print("\n💰 测试账户余额...")
    api_key = "206ea668e69a93830e0b5e9cfa2f9661bb5735da75102"
    
    try:
        response = requests.post(
            "https://api.yescaptcha.com/getBalance",
            json={"clientKey": api_key},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("errorId") == 0:
                balance = result.get("balance", 0)
                print(f"✅ 账户余额: ${balance}")
                return balance > 0
            else:
                print(f"❌ 获取余额失败: {result.get('errorDescription')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取余额异常: {e}")
        
    return False

if __name__ == "__main__":
    print("🚀 开始 YesCaptcha 服务测试")
    print("=" * 50)
    
    # 测试余额
    has_balance = test_balance()
    
    if has_balance:
        # 测试验证码解决
        token = test_yescaptcha()
        if token:
            print("\n🎉 YesCaptcha 服务测试成功!")
        else:
            print("\n❌ YesCaptcha 服务测试失败!")
    else:
        print("\n⚠️ 账户余额不足，跳过验证码测试")
        
    print("=" * 50)
