#!/usr/bin/env python3
# -*- coding: utf-8 -*-

with open('snowflake_register.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 只修改特定的行，不删除任何内容
for i, line in enumerate(lines):
    # 修改等待时间
    if 'for attempt in range(60):  # 最多等待5分钟' in line:
        lines[i] = line.replace('range(60)', 'range(120)').replace('5分钟', '10分钟')
        print(f'修改第 {i+1} 行: 等待时间')
    
    # 修改显示信息
    elif '验证码解决中... ({attempt + 1}/60)' in line:
        lines[i] = line.replace('/60)', '/120)')
        print(f'修改第 {i+1} 行: 显示信息')
    
    # 修改备用 token 逻辑
    elif '# 如果 YesCaptcha 失败，使用备用 token' in line:
        lines[i] = '        # YesCaptcha 失败时返回 None\n'
        print(f'修改第 {i+1} 行: 备用逻辑开始')
    
    elif 'print("🔄 使用备用验证码 token...")' in line:
        lines[i] = '        return None\n'
        print(f'修改第 {i+1} 行: 返回 None')
        # 删除后续的备用 token 相关行
        j = i + 1
        while j < len(lines) and (lines[j].startswith('        ') or lines[j].strip() == ''):
            if 'backup_token' in lines[j] or 'return backup_token' in lines[j] or '使用备用 token 完成' in lines[j]:
                lines[j] = ''  # 清空这些行
                print(f'清空第 {j+1} 行')
            j += 1
        break

with open('snowflake_register.py', 'w', encoding='utf-8') as f:
    f.writelines(lines)

print('✅ 精确修改完成')
