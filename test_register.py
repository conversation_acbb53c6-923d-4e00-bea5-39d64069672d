#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Snowflake 注册机
"""

from snowflake_register import SnowflakeRegister
import sys

def test_single_registration():
    """测试单个注册流程"""
    print("🚀 开始测试 Snowflake 注册机")
    print("=" * 50)
    
    register = SnowflakeRegister()
    
    try:
        success = register.run()
        if success:
            print("\n🎉 注册测试成功!")
            print("=" * 50)
            print("📋 注册信息:")
            print(f"   邮箱: {register.email}")
            print(f"   用户名: {register.username}")
            print(f"   密码: {register.password}")
            print(f"   账号标识符: {register.account_identifier}")
            print(f"   API Token: {register.api_token[:50]}...")
            print("=" * 50)
        else:
            print("\n❌ 注册测试失败!")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return False
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        return False
        
    return True

def test_multiple_registrations(count=3):
    """测试多个注册流程"""
    print(f"🚀 开始批量测试 {count} 个注册")
    print("=" * 50)
    
    success_count = 0
    failed_count = 0
    
    for i in range(count):
        print(f"\n📝 第 {i+1}/{count} 个注册:")
        print("-" * 30)
        
        register = SnowflakeRegister()
        
        try:
            success = register.run()
            if success:
                success_count += 1
                print(f"✅ 第 {i+1} 个注册成功")
            else:
                failed_count += 1
                print(f"❌ 第 {i+1} 个注册失败")
                
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断，已完成 {i} 个注册")
            break
        except Exception as e:
            failed_count += 1
            print(f"❌ 第 {i+1} 个注册异常: {e}")
            
    print("\n" + "=" * 50)
    print("📊 批量测试结果:")
    print(f"   成功: {success_count}")
    print(f"   失败: {failed_count}")
    print(f"   成功率: {success_count/(success_count+failed_count)*100:.1f}%")
    print("=" * 50)
    
    return success_count > 0

def test_components():
    """测试各个组件功能"""
    print("🔧 开始组件测试")
    print("=" * 50)
    
    register = SnowflakeRegister()
    
    # 测试邮箱创建
    print("📧 测试邮箱创建...")
    try:
        register.generate_random_info()
        success = register.create_email_account()
        if success:
            print(f"✅ 邮箱创建成功: {register.email}")
        else:
            print("❌ 邮箱创建失败")
            return False
    except Exception as e:
        print(f"❌ 邮箱创建异常: {e}")
        return False
        
    # 测试验证码解决
    print("\n🤖 测试验证码解决...")
    try:
        token = register.solve_recaptcha()
        if token:
            print(f"✅ 验证码解决成功: {token[:50]}...")
        else:
            print("❌ 验证码解决失败")
            return False
    except Exception as e:
        print(f"❌ 验证码解决异常: {e}")
        return False
        
    print("\n✅ 所有组件测试通过")
    return True

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "single":
            test_single_registration()
        elif command == "batch":
            count = int(sys.argv[2]) if len(sys.argv) > 2 else 3
            test_multiple_registrations(count)
        elif command == "components":
            test_components()
        else:
            print("❌ 未知命令")
            print("使用方法:")
            print("  python test_register.py single        # 单个注册测试")
            print("  python test_register.py batch [数量]   # 批量注册测试")
            print("  python test_register.py components    # 组件测试")
    else:
        # 默认运行单个注册测试
        test_single_registration()

if __name__ == "__main__":
    main()
