输出2个文件
这里密码指的是验证邮箱后让注册的账号密码
然后这里唯一标识符指的是
https://ccdiiyc-gx65500.snowflakecomputing.com/console/login?activationToken=ver%3A1-hint%3A44455436-ETMsDgAAAZhkPg%2B%2BABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEHo9x2flV2mHUAQiXOhxKuQAAABQoQg3LGyJVig3FKglXNk9cSbIReU1feq9GhCF83PmTgeNtbiGsebcjQxvRtC1SQuy0%2B0k3jb5DplmibNRBI46gOO7Fjur69Z1RzR0D3uoHukAFPq616%2BfHyfyDLf4wnTCJMTacKbb</var><var>accountUrl=https://ccdiiyc-gx65500.snowflakecomputing.com/console/login</var></script><br>Congratulations on getting started with Snowflake! Click the button below to activate your account. <br><br><table border="0" cellspacing="0" cellpadding="0" style="margin-left: auto; margin-right: auto;"><tbody><tr><td align="center" valign="middle" style="border-radius: 5px;" bgcolor="#00B6EB"><div id="Register-Button" style="text-align: center;"><a href="https://9rjj6kfe.r.ap-southeast-2.awstrack.me/L0/https:%2F%2Fccdiiyc-gx65500.snowflakecomputing.com%2Fconsole%2Flogin%3FactivationToken=ver%253A1-hint%253A44455436-ETMsDgAAAZhkPg%252B%252BABRBRVMvQ0JDL1BLQ1M1UGFkZGluZwEAABAAEHo9x2flV2mHUAQiXOhxKuQAAABQoQg3LGyJVig3FKglXNk9cSbIReU1feq9GhCF83PmTgeNtbiGsebcjQxvRtC1SQuy0%252B0k3jb5DplmibNRBI46gOO7Fjur69Z1RzR0D3uoHukAFPq616%252BfHyfyDLf4wnTCJMTacKbb/1/01080198643e2aeb-d525bbf5-204c-4915-9a25-97435d3fb9ac-000000/MSeUct9zpWy0DZ7JsskUngMrcDQ=218" style="color: #fefefe; font-family: Open Sans, Helvetica Neue, Helvetica, Arial, sans-serif; font-weight: 500; text-align: left; line-height: 1.3; text-decoration: none; font-size: 14px; display: inline-block; border-radius: 3px; text-transform: uppercase; letter-spacing: 0.5px; margin: 0; padding: 10px 20px; border: 0px solid #2199e8;" target="_blank">Click to Activate</a></div></td></tr></tbody></table><br>This activation link is temporary and will expire in 72 hours. <br><p> </p><strong>Save this for later</strong><br>Once you activate your account, you can access it at <br><a class="mktNoTrack" href="https://9rjj6kfe.r.ap-southeast-2.awstrack.me/L0/https:%2F%2Fccdiiyc-gx65500.snowflakecomputing.com%2Fconsole%2Flogin/1/01080198643e2aeb-d525bbf5-204c-4915-9a25-97435d3fb9ac-000000/VHGSvB_lBNhj9tYupBEuGWR3XAo=218" target="_blank">https://ccdiiyc-gx65500.snowflakecomputing.com/console/login
这个验证链接的 ccdiiyc-gx65500 字母要大写 也就是 CCDIIYC-GX65500
文件1 输出 邮箱----用户名----密码----cookie----唯一标识符----APIkey
也就是token密钥就是***********************************************.******************************************************************************.3YwlmeKy0If-T0cCQHCUOdAdMglvcLGra0g58GMuab-AVRHpb-d-u1iMgp_1cMESvyjqArqZpHHHrdGGIHvJMg
这个 来自 5获取token密钥.txt 那里那样的
然后
第二个文件和我上免说的一样 但是是json格式 要严格json格式

现在开始跟随数字从小到大阅读流程 切记要快 要稳定 允许看一会写一会 和测试这一块功能是否可用 严格去写 严格的测试 测试都以实战为准 而不是自己搭建环节 而是真实的去注册
开始写注册机吧
