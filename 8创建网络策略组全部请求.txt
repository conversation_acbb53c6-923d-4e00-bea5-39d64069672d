curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-<PERSON>&isEnterpriseClient=false' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: content-type' \
  -H 'access-control-request-method: POST' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["Loading…","2:26:38 PM","UI initializing... ","duration","duration-timer","0."," s","Start time","Aug 1, 2:26 PM","ID","——","Warehouse","Rows produced","Bytes scanned","cancel button","Cancel"]' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v0/queries/3tG8cGGSvgs/execute/sql' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/x-www-form-urlencoded' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-000000000000000035844f5855707706-1ed7fbad89f6219d-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-csrf-token: 790aaad3' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-numeracy-userid: *************' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-page-source: worksheet' \
  -H 'x-snowflake-request-id: d27214f1-c0ba-42e7-ad91-fa784528168c' \
  -H 'x-snowflake-role: ACCOUNTADMIN' \
  -H 'x-snowflake-role-encoded: 1#ACCOUNTADMIN' \
  --data-raw $'projectId=3tG8cGGSvgs&&query=USE%20ROLE%20ACCOUNTADMIN%3B%0ACREATE%20NETWORK%20POLICY%20ALLOW_ALL%20ALLOWED_IP_LIST%3D(\'0.0.0.0%2F0\')%3B&queryLanguage=sql&&queryRange=%7B%22start%22%3A23%2C%22end%22%3A85%7D&paramRefs=%5B%5D&transforms=%5B%5D&action=execute&executionContext=%7B%22role%22%3A%22ACCOUNTADMIN%22%2C%22secondaryRoles%22%3A%22ALL%22%2C%22warehouse%22%3A%22COMPUTE_WH%22%2C%22database%22%3A%22SECURITY_DB%22%2C%22schema%22%3A%22PUBLIC%22%7D&snowflakeRequestId=e3cf1d53-3197-40f2-9d1a-705c174174a4&clientOperationId=cff5a072-b597-48e5-b12f-35c9138d315c' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["4"]' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["5"]' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["6"]' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/queries/01be12a2-0002-a728-0000-0002a6566071?includeFirstChunkData=false&computeStats=false' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: traceparent,x-numeracy-client-version,x-snowflake-context,x-snowflake-context-encoded,x-snowflake-request-id' \
  -H 'access-control-request-method: GET' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/queries/01be12a2-0002-a728-0000-0002a6566071' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: content-type,traceparent,x-numeracy-client-version,x-snowflake-context,x-snowflake-context-encoded,x-snowflake-request-id' \
  -H 'access-control-request-method: PATCH' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["Results inspector","Calculating stats…"]' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/queries/01be12a2-0002-a728-0000-0002a6566071?includeFirstChunkData=false&computeStats=false' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-00000000000000004f85dd50ae18666d-78d0025364def7e9-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-request-id: 40b9cb87-d3cd-4f99-8b65-65b5ecf355db' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/queries/01be12a2-0002-a728-0000-0002a6566071' \
  -X 'PATCH' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-0000000000000000f360aa83f4df7213-3983cff4e169a2c0-01' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-request-id: 3bb02bda-41e0-4f98-a92e-92c81caa189d' \
  --data-raw '{"mostRecentTransform":{}}' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["Select columns on results","Hide stats","Search table results","status stats card","status","text","Selected","100% filled","Contents Meter Segment"]' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v0/queries/3tG8cGGSvgs/history' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/x-www-form-urlencoded' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-00000000000000000389d0cc8540ad1f-1364cd8a206a52fc-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-csrf-token: 790aaad3' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-numeracy-userid: *************' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-page-source: worksheet' \
  -H 'x-snowflake-request-id: e92c6179-1c29-4349-b876-c6da424dd7b4' \
  -H 'x-snowflake-role: ACCOUNTADMIN' \
  -H 'x-snowflake-role-encoded: 1#ACCOUNTADMIN' \
  --data-raw 'projectId=3tG8cGGSvgs&action=history' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v0/settings' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: multipart/form-data; boundary=----WebKitFormBoundaryAuOeKSs69FuPuN8F' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-000000000000000026cb87bf85f83cec-12fb2e68aea97143-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-csrf-token: 790aaad3' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-numeracy-userid: *************' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-page-source: worksheet' \
  -H 'x-snowflake-request-id: b2c00843-25d7-452a-b010-796688301a89' \
  -H 'x-snowflake-role: ACCOUNTADMIN' \
  -H 'x-snowflake-role-encoded: 1#ACCOUNTADMIN' \
  --data-raw $'------WebKitFormBoundaryAuOeKSs69FuPuN8F\r\nContent-Disposition: form-data; name="lastUsedQueryContext"\r\n\r\n{"role":"ACCOUNTADMIN","secondaryRoles":"ALL","warehouse":"COMPUTE_WH","database":"SECURITY_DB","schema":"PUBLIC"}\r\n------WebKitFormBoundaryAuOeKSs69FuPuN8F--\r\n' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v0/settings' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: traceparent,x-csrf-token,x-numeracy-client-version,x-numeracy-userid,x-snowflake-context,x-snowflake-context-encoded,x-snowflake-page-source,x-snowflake-request-id,x-snowflake-role,x-snowflake-role-encoded' \
  -H 'access-control-request-method: POST' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["189ms"]' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v0/session/query?desc=show-warehouse' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/x-www-form-urlencoded' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-00000000000000001a53d83a6016262c-5481b3eadcf27f30-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-csrf-token: 790aaad3' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-numeracy-userid: *************' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-page-source: worksheet' \
  -H 'x-snowflake-request-id: 6c88c24e-7c33-431d-bf0f-4ca3924a5196' \
  -H 'x-snowflake-role: ACCOUNTADMIN' \
  -H 'x-snowflake-role-encoded: 1#ACCOUNTADMIN' \
  --data-raw $'sqlText=show%20WAREHOUSES%20like%20\'COMPUTE_WH\'&bindingsJSON=%5B%5D&role=ACCOUNTADMIN&&&&&&&pid=3tG8cGGSvgs&secondaryRoles=&&isInternal=true&requestId=6c88c24e-7c33-431d-bf0f-4ca3924a5196' ;
curl 'data:image/svg+xml;charset=utf-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M7.10097%203L4.31836%2011H5.37712L6.07278%209H9.84055L10.5906%2011H11.6586L8.65855%203H7.10097ZM9.46555%208H6.4206L7.88584%203.78744L9.46555%208Z%22%20fill%3D%22%238A96AD%22%2F%3E%0A%20%20%3Cpath%20d%3D%22M4%2014H12V13H4V14Z%22%20fill%3D%22%238A96AD%22%2F%3E%3C%2Fsvg%3E' \
  -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'Referer;' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v0/queries/3tG8cGGSvgs/monitor' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/x-www-form-urlencoded' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-0000000000000000703e5f5edeb1bfff-71309cb04f9bbf0a-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-csrf-token: 790aaad3' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-numeracy-userid: *************' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-page-source: worksheet' \
  -H 'x-snowflake-request-id: 88de649e-7ff0-4ca4-af3c-2d382c966116' \
  -H 'x-snowflake-role: ACCOUNTADMIN' \
  -H 'x-snowflake-role-encoded: 1#ACCOUNTADMIN' \
  --data-raw 'action=monitor&projectId=3tG8cGGSvgs&snowflakeRequestIds=%5B%22e3cf1d53-3197-40f2-9d1a-705c174174a4%22%5D' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["157ms","Copy","01be12a2-0002-a728-0000-0002a6566071","Show more"]' ;
curl 'https://edge.microsoft.com/translate/translatetext?from=&to=zh-Hans&isEnterpriseClient=false' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: application/json' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: cross-site' \
  -H 'sec-mesh-client-arch: x86_64' \
  -H 'sec-mesh-client-edge-channel: stable' \
  -H 'sec-mesh-client-edge-version: 138.0.3351.109' \
  -H 'sec-mesh-client-os: Windows' \
  -H 'sec-mesh-client-os-version: 10.0.26100' \
  -H 'sec-mesh-client-webview: 0' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-edge-shopping-flag: 1' \
  --data-raw '["Download results"]' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/queries/01be12a2-0002-a728-0000-0002a6566071/chunks/0' \
  -H 'accept: application/json' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -b '__stripe_mid=6ba4eb68-69c3-4bc4-ad36-46dc7b1d2476f3f27e; S8_SESSION_HEWSEEP__https___og38984_ap_northeast_1_aws_snowflakecomputing_com=ZDlkNTdjZDktMGNmYi00Y2Y5LWIyMTYtMmVlODNmZjdlNzdi; user-686577736565703a3a68747470733a2f2f6f6733383938342e61702d6e6f727468656173742d312e6177732e736e6f77666c616b65636f6d707574696e672e636f6dcbf29ce484222325="gQhyazZDVWJvNtkVgYwOYcVHwXTG4WmoDS3GcTmTfsN8NmCP1S-XyBpyR0Ot3-OR6WJiEn4MsbuBBhS8U9BPV0asfWKXdSf0M95v3J3ECEhWi4vXOEkA_VdKmxpXmv140X42FBny5oUhccgzf3-fs5FBzF23p35dWa-Ll2Edri399hfAZpPPT0C5US9gyJ9DDA=="; csrf-790aaad3=790aaad3' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'traceparent: 00-00000000000000007a57f5b003544510-5fe664f9a45f809a-00' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  -H 'x-numeracy-client-version: 250728-1-d3ed3f3db5f' \
  -H 'x-snowflake-context: HEWSEEP::https://og38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-context-encoded: 1#HEWSEEP%3A%3Ahttps%3A%2F%2Fog38984.ap-northeast-1.aws.snowflakecomputing.com' \
  -H 'x-snowflake-request-id: 67ea7af9-bda7-46a8-8a85-103bcc6850f1' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/queries/01be12a2-0002-a728-0000-0002a6566071/chunks/0' \
  -X 'OPTIONS' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'access-control-request-headers: traceparent,x-numeracy-client-version,x-snowflake-context,x-snowflake-context-encoded,x-snowflake-request-id' \
  -H 'access-control-request-method: GET' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' ;
curl 'https://apps-api.c1.ap-northeast-1.aws.app.snowflake.com/v1/observability/replay?ddsource=browser&ddtags=sdk_version%3A5.35.1%2Capi%3Afetch%2Cenv%3APUBLIC.AWS_AP_NORTHEAST_1%2Cservice%3Asnowsight%2Cversion%3A250728-1-d3ed3f3db5f&dd-api-key=pub20e29d3f014792094e8ccc864bc57d8f&dd-evp-origin-version=5.35.1&dd-evp-origin=browser&dd-request-id=ba2cbfbd-33f2-4fcf-bc82-29d21fed916c' \
  -H 'accept: */*' \
  -H 'accept-language: zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6' \
  -H 'content-type: multipart/form-data; boundary=----WebKitFormBoundaryOZ8F1uuct7AyTN67' \
  -H 'origin: https://app.snowflake.com' \
  -H 'priority: u=1, i' \
  -H 'referer: https://app.snowflake.com/' \
  -H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "Windows"' \
  -H 'sec-fetch-dest: empty' \
  -H 'sec-fetch-mode: cors' \
  -H 'sec-fetch-site: same-site' \
  -H 'user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' \
  --data-raw $'------WebKitFormBoundaryOZ8F1uuct7AyTN67\r\nContent-Disposition: form-data; name="segment"; filename="ddd19870-ab86-46be-a937-5a1fbbc9098f-1754029597828"\r\nContent-Type: application/octet-stream\r\n\r\nx\u009c,ÌA\nÃ \u0010\u0005Ð»üµ\u000bk\u0015\u0093¹D\u000fPº\u0090hÀEªè\u0014\u001aÄ»g Ý?Þ@K[i±\u0083\u009e\u00031p\u0000\rôòi[\u0002Ý\u0014jé\u0099syÿ@\u008e ëï\n\u009c\u008fôØ÷\u009e\u0018¤\u0015¾b\u008d\u0015~\u0082\u009c\u009e¯)â¬2üiçpT1ÞYmV·úÅ,ó\u0002\u0000\u0000ÿÿÒÁ´\u0010hTb\nØ1±:@\u0097åæ\u0097¥B9%©\u0015%PfbIIQfRiI*\u0092\u009bÌLPÅ«\u0095\u0092s\u0012\u008b\u0081\u000c¥Ä,\u0005d\u0090\u009d¢P^¬\u0090l¦\u0090\u009d¦\u0090\u0092¤TK\u008c[Í\u000ck\u0001\u0000\u0000\u0000ÿÿÂâVì\u0081cf\u0081=p\u000cL\u0021\u0081cB\u008c\u0085\u0016Æµ\u0000\u0000\u0000\u0000ÿÿ"ÞBì±ah\u0009\u008d\rs",´4¶¨\u0005\u0000\u0000\u0000ÿÿ¢ØBs\u0013\u0088\u0085\u0016ÄXhiX\u000b\u0000\u0000\u0000ÿÿ¢ÜB#¢-´0\u0000¦J\u0000\u0000\u0000\u0000ÿÿ¢\u0096\u0085fD$p\u000b`\u0004Ô\u0002\u0000\u0000\u0000ÿÿ¢ÜBc\u0088\u0085\u0086DXhdhT\u000b\u0000\u0000\u0000ÿÿB²0#±8>-?¹\u0014hKIQi*\\»\u0019\u000eí&F¦µ\u0000\u0000\u0000\u0000ÿÿÒ\u00818Ç\u0014\u0094VÑ\u001d\u000eô:HÎÜÜ\u0002f\u0092)A\'\u0081Ì\u0004\u0000\u0000\u0000ÿÿ\u0082\u009bi\u0086ÓL\u0088÷Á&\u0019bx\u009c \u001dÆ\u0006µ\u0000\u0000\u0000\u0000ÿÿ\u0082ÛaN\u008c»Í\u00880Ó¸\u0016\u0000\u0000\u0000ÿÿ\u0082\u009biA\u008c»\u0089\u0008\u000bc\u0093Z\u0000\u0000\u0000\u0000ÿÿ\u001a°\u0002¯\u0084¤\u0002ÏÂÄÌ¨\u0016\u0000\u0000\u0000ÿÿ¢¢[Í\r-LÑ\u001d[\\R\u0099\u00034R©$¿ÀJÁÐØ¨ ÂZ\u0021\'5­ÄJÁØØ\u0010ÄÉ/HLÎ,©\u0004JZ+\u0094gæäè&g$æ¥§ZÁ$t\u0014J\u008a\u0012ó\u008aÓò\u008br­\u0011L+\u0085âäÄ\u009cT\r\u0003=Ks3\u0013\u0013\u0013Mk"=l\\\u000b\u0000\u0000\u0000ÿÿ\u001aê\u001e6355µ° ÖÃ\u0016\u0096µ\u0000\u0000\u0000\u0000ÿÿ\u001aê\u001e6544µ 6\u0086M\r\rj\u0001\u0000\u0000\u0000ÿÿ\u001aê\u001e6¶°404\'ÖÃÀ\u0006\u0016\u0000\u0000\u0000ÿÿ\u001aê\u001e6²05&6E\u009b\u009a\u0098Õ\u0002\u0000\u0000\u0000ÿÿ\u001aòþ5°43$:E\u009b\u0099Ö\u0002\u0000\u0000\u0000ÿÿ\u0082WR\u0096ÄTR\u0098­\n\u0082v\u0098\u001bÖ\u0002\u0000\u0000\u0000ÿÿ\u0082Ùah@\u008c\u001dFdØa\\\u000b\u0000\u0000\u0000ÿÿ\u001a4\u0011g\u0080\u0016qxã\u008bÈ\u0088277ª\u0005\u0000\u0000\u0000ÿÿÌ[[o£H\u0016þ+\u009fò´ûàn®6îy\u009bÕ¨5Ro¿¬´o«\u0016PÅµ\u0000\u001b0¾\u008còß÷\u0014Ø\u000eØ.ÇNp&\u0091:\u0002\u008aªúÎùÎµH_\u0015ð¯§\u009cDú\u0093Àç+\u0021¨@tK\u009e·÷ºCUS^0.\'\u001e\u0015\\»áO7\u0093\u0002±¸yR\u0009{(3¿ÁõªBÐðoôî¯¬ª£\u00981\u009eÓ+Æ\u0013\u0081÷£X°\u009f´G\u0007åÆ]\u000eÅ\u0006KÁ\u0004X\u0006\u0096\u000fª\u000e?\u0080ç\u0080\u0015`\u000b¸\u0002nF+\u0094E\u000b¬ä\u0021Á\u0092+\u0096±;\u0011®ÇE»\u0089+\u008apòëWR\u0019&U\u008bO\u009di=õ\u001f\u001c\u0081\u000f&\u000bÎ¼mïMûTL}ÿ`°\u009faZ\u008eí\u001c^u«\u0088\u009e\u0099\u009aó~\u008d¤³\u0081"X\u0009V\u0081Õç Þ¯û\u0015X\u0003¶\u0006Û\u0080mÁvà\u001a¸\u000e?\u0085G\u0015\u009f\u0080\u009fÁÏáåð\u000bø\u000bp\u0003~\u0003nÂ_\u0081[ ê¸\r>\u001dÀå3p\u0007^ü\u0018¸©\u0003î\u0082{\u0003\u0088Ü\u0007gà\u001c<@:\u0007\u000fÁã\u0021¦\u0004<\u0085\u009b\u008fÀ\u008d{\rBXK\u0008q\u000b\u0021õ\u0090ú§@.@øß¾½0¬ùóñºë5ïÄ6\u0090xùâ.uQ\u0088:^\u009cú\u0083µgHFÃ½ùR 1M\u0095ñ\u001fM|®T£ÙÅÖ\u007f\u0015yMñ\u0087^Þ´?OG±ô\u009e\u0088FïÚì][½k»w=}\u0096:Q\u0005:{:»;Ð\u001d\u0014çg\u0093pEz(\u0011Åa$è_=Y®x¹\u009d\u00942\u0088ï\u0007ßo<Ç}þ\u0010<#Üà\u001bîÓKl"â\u009cÓ´Cä\u008d¸Ä@ÉD¦\u0096&®b/\u0016mvéHù\r\u008b"&\u0015\u0097\u0013ÞÐ2Õ7äENÁYaZöÔz\u00835]\u0007K6?qý:nø\u000fºûÞ)è\\\u0000Ý\u0091\u0012dn\u0019Æù¤M\u009e\u0086Ì\u009dj¤GÂíé\u0090oº\u001f\u009fnº\n\nÁ¾?\u0088ßñ\u0019½\u0008©jÂ3Hë\u0098ÕÒ[õ6ëµ\u009b\u001fî\u009a\u0098¯\u007f/¨ÆzÒ A\u009f¢}\u001aPÑB\u008fä¶-ì²HùËý&\u0013ò´é)ªëÅ·¯_×ëõ\u0097µù¥(Ã¯\u0086¦i_;\u0004\u00075\u0004\u0016\u0002\u001b¾;Ìb\u0015|\u001f>\u0083O¶\u0013¼\u0004§EÉ+\u0092Ø­»\u008c~,´^j¥² Qþ\u008fÉ\\c<üç\u008d:Y¸$þ©Rdôû·\u0085é\u0017û\u0087\u0003\u009dú¿\u001fº\u0021ï\u008e\u0002·¦í»\u000bYc\u0014«\u009c\r\u0007\u0012¢è8rÉ\u0082«ÿü÷{wXv\u000cZ³gÅ\u0080Ó\u008bló1Üó\u0021\u000e93äQåñZ\u001f¸¤|ð\u0010\u009f\u0094Êþ¹Ê<^V\u000f¨"Æ÷Ò³<jëÎìµ4jkw¤Ñc\u0006\u009dö²fwä4¾ôç6bI\u00139\u0093Ò²ìù\u0089PÖ=µÁ\u008bP½\u0094ßõ¤\u001fè\u000c\u0017ä²\u0089\u009b\u0013¹Ôeý5¹¦=¹f½kgàGÖtÞw#k¦½Ù\u008d\u0084×\u008f¹W¼z>D`;\u001f\u008d`¦=?\u000fúò¿N\u0001ì«âö\u0010êòÐl8Ôöºí\u00906\u009f\u000e\u0087ÚÐ´\u009f¥\u001e2ç\u008a\u0021»còa_\u0010÷\'$º5\u009fÝRÜGö¾#<Î´låÞé\u001aé\u0006é\u0016éî¦\u001eOÌàÓh-{<jö\u0084C\u001bz=R#\u0081\u0080V\u009aÁ\u009b\u0081Í©Ñ\u0093\u00820\u0097\u0004\u0001ó±\\c¹ÁrÛåø¶_fqåzÔb·Y}Å{ å\u0017\u0015\u0085C\u0087ð#ø´vr7h¾:AÌ¦×\u0010ç\u001erúÍä\u0081C\u0016½\nZwæj\u008aú»z><Ö\u009b\'¿¦^\u009e\u00170¼2Õ|ëTC»bPÃy½I\u0086rR\u009f\u0017·\u0086»\u0082[Á-\u0095¼øKPJ\u000f\u0096(ãÖ\u009e6ðÉ4\u0088\u000c\rL\u00073ÀL0\u000bÌ>\u0013\u0082\u009eS\u009fÝìÙòé\u0085­d\u008b¯Á8XÐCk*Ñ\u00120\u0009¯ÅIæQ\u009a-\u0018º®@\u008a\u008b\u0088òõ\u0005õ\u0099ð5\u0004þ`ç ¿¡5ýà\rí\u0087H(®íiêê`ö\u0010\u0021MCiä\u000fÚÐüè\r-ã\u00837´?ZBu\u0006\\å~\u0021\u0084»¨øï5\u0005\u0087ûÃH?\'\u0011¸\u001b#\u0009\u0085~\u0002N¡\u009f\u0082Éyèg¬\u000b&X\u009a=\u0021\u009c\u009b²\u0092\nvX"¬Ú\u0083ÀLÂ\u000e\u0009p\u0089R Ì\u0010$\u0008\u001b\u0004\u001cá\u001aá\u0006A\u008ap\u008bpw9ô\u001dÑFÚ\u001eí\u0021ô\u0021Ò{9T?ûVq@\u009b$HR$\u0002I\u00864D\u001a\u0021Y Y^Vr\u0087¶S²»\u0085»CR"©\u0090ÔHVH\u001a$k\u00095Ù Ù"Ù\u0021Õ\u0006°Sã\u0014yj¾è9µ\u0090ÚH§H\u001cD\u008b\u001exS\u001b«j9±\u0010¡Ý\u008d_e$\u0007ðýbKi\u0021ï\u0084ý\u0006\u009d«Ê\u009a\u000b°§JØÂ\u0087`\u0010\u001c"\u0090¦ÒÁ¾ÝTD\u0008\u0011AÄ\u0010\u0009D\n\u0021 2\u0088\u001c¢\u0080 Úl\u0009QBT\u00105Ä\n¢9\u0015D¬¯9©Ø@l\u0021v=A\u001ce\\{[Ý(\u0003ß¡tÌæÔù"ó\u0090ùÈ\u00182\u008e\u008cêÂð¾hs\u0091\u0091hº÷_ª2{²(ËÉwÊBæô8YÎË0k®\u008cý÷\nrâ\u0014£HqÑ´Î¥°5e\u008e¾E\u008a\u0093àÿwI1\u009b+\u008d\u008a\u001eÔE>ñ\u008b¼.\u000b\u0081¬FF¡&\u0097Ù\u0081odúÏ(C5{ÌÑ\u000eÙ\u001aÙæ¤\u00120\u0091m\u0091íZLÔ9iÈuä\u0086\u0084\u0015\u0011yZ¯q14¥§Þ\u000b¤*P-Î\u0080äSä³×\u0081\u0018\u00969Z«y\u009eir\u0017\u008a\u0086ó"_½\u0086ó\u0005¡c\u008cì>¤».8\u0093îîBÇ/¢\u001bS}^\u0001w³Gçi# SWò\u009fAwc\u009b^\u001fÝGF\u0015G}Æ\u0094\u0011©\u0015Ò\u001aE[j\u00169\u008aâjý Ã\rP,\u0010ë(\u0096(Ê3Ç.*x\u0091l7\u0008YAu\u0006­¸Bº\u0094àÚ?\u0006AÑ  ]\u0084ýÓ3u-Ì-$&­\u001cO\u0007\u001bÉvf×ö5\u0001\u0016\u008cR]OÚ©ò¼¦[lÁ/-·o\u0093\u0002\u0099í\u0007ËÍ\u0094\u0087\u0006½ÏeýÅ\u0082hhFã\u001d\u0096\u0011~rÁ¿Ë\u008cts®¬úÏÌ\u0080lÀ³¥^ãµä=*ûtkJ\u0086Þ \u0091|-c:u¾Ñ\u0016u\u0083zý\u009a\u0090Ô;R\u0083ÅZ¿?Tv,F½ÃJ\u001bt9\u0004t´\u001a¯\u008b\u009e}êV\u0006Væû\u0008qì[\u008f4¯\u0013âÌ\u0094Ä~.BFô¥Ç\u0010b(\u0083Å}\u0084¨\u008fF?\u0019\u0021\u00962r\u007f\nBLõ\u0009ì]\u0084\u0098ê\u0016üs\u0011bªÓÕç D].ßG\u00889\u009aá=\u0098\u0010kÌ\u001aw|B\u000cõç­û\u0008y_\u0003þ\u0081\u0084è£%»\u0087\u0010¢\u001bïª\u008eú\'\u0019\u009d"£Õàôúñ%#\u0096Ù@\u009ewyê\'\u0094ç]Eáç\u0093Ç\u001c-ctòðúå\u0090%J>¶E±Õ­\u0085\u000c)\u0015\u0012\u008e³°Ö6«ýEte(ãó\u0097¿"_íÐhht4\u0006\u001a\u0013\u008d\u0085ÆF3\u001dh\u0084ÚÍf\u0086Æ\u0019îÙÌ¥X\u008d\u008bÆCZ¢¡kêaÍÛþKÉ\\\u007fþ?\u0000\u0000\u0000ÿÿ"~Ï\u000cÊ¢\u0018Ø~X`¿\u0001ß¢\u0018Ân°04ª\u0005\u0000\u0000\u0000ÿÿ¢Ú\u0006g#Â\u009e¶4°0©\u0005\u0000\u0000\u0000ÿÿ"ÚB\\g\u0014\u0018Z\u0082-´0#ÂB`Bª\u0005\u0000\u0000\u0000ÿÿ¢ê\u0096]\u009c\u008d\u0000ä\u0019\u001c`\u001eËÊ\u0007Möaæ1`\u0099\u0081}\u0014\u0093ô\u0019\u009clCÊ&qHY-\u0095k@ÒöcKPÒ\u0004\u0000\u0000\u0000ÿÿì\u009cK\u0092\u009b0\u0010\u0086¯¢e² JBB\u008f\u001bx\u0091]r\u0001\u008c\u0099@\u008a±§xL\u008d\u0017s÷H\u0018\u009b¶@6hj<¤ÂÎ\u00851n¤æ·Zîï÷\u0004¦Øm^*\u0089÷¯qåD¦vyõRÄÇ®1ô\u008a\nëiª§ü-ÝÝh¹\u0015v\u009b­ K\u008b0´#\u000c\u0097\u0016\u0021½I\u008bü§X\u001c\u000bÛ¾ä\u001e\u0003ê\u000eLÀâô\u0099|"\u0016Ç\u0085\u008a¬þYÁV,nÅâ¼°8\u0001\u009aÃ\u0085OsøÕ\u001dßÁât\u0096GÃ\u0086ðIX\u009c\u0088¼±8\u0001úÄ\u0005è\u0013\u0017\u0000\u0012\u0011\u0080\u000e\u0094\u0080É\u0090ö\u008f\u0085$«Ð\u0085\u000c+y=±§\u0003S\u0084\u000e+\'»a\u000b\u009d¤ÌÊ\u0002îÎ\u0082UèV¡»%t\u0012 =Ò\u0007í\u0099%tXñ¡ÐéºWâ{BÇù<¡»È\u009c\u0004J.\u0001Ù+\u0081üI \u007f\u0012È\u009f¼\u0006VÚv\u0089^å$UÞÀ\u008aNw\u009dôµ@µDµê\u001e\u0083:6](û\u00185¿á¨ÂÍK]äè4Ð³o2áÙd\u0085N\u0006]Øë¬8ýC½ËMoJ\u0093¡&7uËe:ª4.\u0093l{0\u0003c*\u0094@\u0007T·3S¦USÔUp:ãÜ\u001bcëÜÏö]\u001dâV\u0097{ÝGÆô\u0089*Ì\u0005·&/\u001aYÄ}ø¡hþ J¡Ñ\u0081jt1IÚ}Õ-Ê|¿{\u000cp\u001d§X/ÿ©×hÏ`@NàÔÄ³\u0099;\u0016i\u0095\u000c\u00022\u0017ú¥ÓsÓ­\u0019\u0018ÇbxýîÀùQ\u0012Þk\u0006\u0085\u001ddéicg¬ìÊË¤H\u0087\u0093hÆð\u0004Á&ÇËËÒ¨\u0001 cÍç\u009bÒ<oÉ¡8\u0094=\u0018{\u0006\u008dC\u008b\u0021\u009eÆÇªÐ\u0011¬\u009bß%\u0011"Ñ\u000fCï¶\u0008ï¼\u0008\u001d\u0098ïÄ`©kÈ\u0081°) ljµ,X-\u000bVË\u0082Õ²à_²,P.Ë\u0002\u0005\u0096cjÁ\u0096\u0005\u0014÷å1ÅVyLñjYàkYÀ\u0088\u000fÝOq\u0008¦cq\u0096\u0005\u008cø ý\u00143pSK´,`dÎÚ²¿/\u000eîK\u0080×_mY@ñW[\u0016P2Ñ²@`§e\u0001·\u001c\u0006ÚÂõL(¸Ü\u000c\u008cõ\u0095Ë²\u0080K÷[ä#n\u00063¼\u00082\u008e2\u0085²øAv\u0004³ ¹¬\u0098\u0004É\r©ÿ§¸¨\u0000ö/É ]ã²àx\u008dËoAPgú9Þ\u0005eÚ´×\u0008ÌðêJ^\u0017ñ»¸<~\u00870Å sÈÿR\u0083>\u0005ÿK}"\u0080ò\u0080æ\r×\u000c\u000e6O\u008aÔ$Ó¡h\u009e÷\u0095\u0021\u0015nì D\u0011¡\u00046ñS·\u0087ÃØ\u0002²)\u001eÇ©,g\u00887¹>]\u008fäø\u0098\u0012Ê\u0085\u0000¸õç\u0099\u0080<\u0086ät+Çý6\u0085(\u0014ï\u007f\u0001\u0000\u0000ÿÿìÛ1\u000e\u0080 \u0010DÑ3Í\u0010\u0083\u001eÎ»\u009bmtbÄàF\rÅt´Ûñ\u001flò\u009b\u0082íÀv`;°\u001d\u000ce\u0007\u0005¸¶\u0083\u0002i@¼Ñ\u0080_Ù\u0001¤ë0\u009dì ö\u000bm\u00079;`*³\u00219\u008a:\u009c\u001d0ÕØ\u0098e¨¿\u0021­Ë\u000eøä\u0089w\u009f\u008bân\u0084\u009cyÓÍG°\u008614Z6\u0018/Õ²\u001dw°º¬\u001b\u0000\u0000\u0000ÿÿ¢â\u0012],Ý(\u0094æïÓ9+\u009eï\u009eülÞ\u009c\';º^6t>ë^ù´c:Þ.\u0002\u008aö\u0097³&¼\u0098Þÿ|÷ü\u0017ë\u0016>\u009bºáYï:¤S\u001f\u008c,1\u0096à£h~6aÎó-\u008b^,\u0084º\u0080¨&ª\u0099\u0085A-\u0000\u0000\u0000ÿÿÂ\u0012<\u0016:ÐUÿà\u0015É°ÆjQ)8½é%\u001a\u0098U\'%&g§\u0083ë\u000eÝÌÜÄôT+ÝòÔ¤ìÌ\u0012pi\u0097X¤\u009b^\u0094\u0098\u0092\u0009\u008cM\rp=§£ llja\u0090f¤` \u008a`\u009b\u0099è\u0001\u001d`hjdaffdbd`\u0002\u009235LNL4Å+gh` ªi\u008dé\u0082Üüª\u0001´\u009e^6×\u00822d^J*°ÉciLÄ½_ \u008b¿j\u0001\u0000\u0000\u0000ÿÿì\u009dÁ\u000e\u0083 \u0010D\u007fIw\u0006Å\u008fóß\u001bÒ\u0004§\u009b\n\u0094öÖõè\u008dÄ<wà-;\u0019CþU/ãá<Úç\u008b\u0011½\u008cU¼éée{Ên¼\u0004\u0081ÐËB/\u009bÑË`\u0090?òLB~YqO/ã1éÑ\u0012÷ªxÇ\u0089\u0081%Y¢T\u008e&\u0007\u0019&Å\u0097]N-°¸b¿ÜM\u0017 £o\u0018àpÃ\u0000\u0087\u001b\u0006J²r_Aã"ñ\u0000]\u0080®\u0005:HôÀ\u009d_ö;Ð½k\u0018Ø¬\u008c\u008eì\u0091î£\u0009\u0000\u0003Oå \u0004õ\u0090\r\r\u0008\u001f\u0021|\u0084ð\u0011¹\u0011ÖäL2¯ßå®L;\u001f\u0000\u0000\u0000ÿÿ\u001amt\u0092T\u0016\u009b\u009a\u009b¢¦=\u0088\u00001e±©9ÎÔ\u0088±yË\u0018}4À\u0094\n\u00212Z\u0016\u008fÌ²\u0018©\u0085eBë;Í\u0080©Ü\u0018³,&ªÑi\u008a»_E¨Ñi\u0082TÝ\u0098 \rs\u009b \u0015Â&H\u0085°\u0009R\u0021lbFTa\u000bj\u009cRTØZ\u009a\u0098×\u0002\u0000\u0000\u0000ÿÿìÝ±\n\u0080 \u0010ÆñW2õSÚ{\u0091 1\u0092¦¦Þ½\u001c\u0082#:\u000b#\u0094¸Ù©É\u008eÿ\u000fOB£\u0084F\u0009\u008d\u0012\u001a\u007f\u0011\u001a-\u0083\u0094\u008d%ã½­\u0019)\u0083Ä\u0012\u009c\u00912\u0004)g\u0087FÇ\u007fJ¢]\u0091m¤\u0006õ\u0021e\u0097³,Î\u0090Uª\u0006U"ed\u0089r\u0090Ù\u0015dvEq¤\u008câHÙ=CÊ\u001133H9Jk®ÃjÍ\u001e5xÃ\u008d\u0013O\u009e^^{s \u001bÆ\u008eéµ\u000bË4\u0086~H\u0099NÝzxEÝë\u0087O\u009b\u0017D\u0099·q×í?\u0018^­\u001b\u0000\u0000\u0000ÿÿ¢êÜ7þùç\';º_ìÝKìä3Ð} \u000b\u0014\u0001\u0000\u0000\u0000ÿÿ\u0082]Ì`h¨\u0083áPèId\u0090³ÁÀ&\u0099\u00112ÓÐÔÀ´\u0016\u0000\u0000\u0000ÿÿBò3° \u008aOËO.\u0005º\u0016\u001cnpýf8õ\u009bÕ\u0002\u0000\u0000\u0000ÿÿ\\]ÑªÃ \u000c}ßgìy\u0081vµ\u001a÷/C¢Æ»ÂÖ\u0016íåþþL-cÜ\'A=\u0010B<9\u0007\u001fr\u0017Ê£¼}Y,³×\u0011ÏñßÝúL3ï"ÌÕµ,Ò±\nÿ\u0008\u0099ºø\u009bÛÉszMÒ02\u0087%ÇâBUB\u009bL"º´øª%se¦µ<\u0096í\u0088óøâuÓìDw\nÕ\\>y9û¼ü\u0095\u009d\u009ai]\u009fSh*ðÖRyN\u009a\u0083\u001e\u0082\u00824ê\u000e\u0094Ç\u0001H%\u0084ºß+¤Ñvi·Ã\u0085KùÆÅ\u0018{\u008b¦\u0003ò¨AiÏ@v00R\u009f¼\u000f¶³\u0098\u0004×\u0002:@\u001a«ùÄÈÀ¡\u001fA13øZyp½\u0012\u000e&x£\u0095\u008c3:½\u0001\u0000\u0000ÿÿ\u0003\u0000\u0012à®X\r\n------WebKitFormBoundaryOZ8F1uuct7AyTN67\r\nContent-Disposition: form-data; name="event"; filename="blob"\r\nContent-Type: application/json\r\n\r\n{"raw_segment_size":36423,"compressed_segment_size":4892,"start":1754029597828,"end":1754029601506,"creation_reason":"segment_duration_limit","records_count":38,"has_full_snapshot":false,"index_in_view":45,"source":"browser","application":{"id":"f6ec63c4-f560-4b83-a4f8-f6e148a590f3"},"session":{"id":"ddd19870-ab86-46be-a937-5a1fbbc9098f"},"view":{"id":"689478de-ec15-4eee-b000-22a837cb7641"}}\r\n------WebKitFormBoundaryOZ8F1uuct7AyTN67--\r\n'